const Point = require('../models/Point');
const Community = require('../models/Community');
const User = require('../models/User');
const Data = require('../models/Data');
const {
  ValidationError,
  NotFoundError,
  AuthorizationError,
  DatabaseError
} = require('../../utils/errors');

/**
 * Create a new point
 *
 * @param {Object} pointData - Point data
 * @param {string} pointData.firebaseUid - Firebase UID of the creator
 * @param {string} pointData.communityId - Community ID
 * @param {string} pointData.username - Username
 * @param {string} pointData.email - Email
 * @param {string} pointData.phoneNumber - Phone number (optional)
 * @returns {Promise<Object>} Created point
 */
const createPoint = async (pointData) => {
  try {
    // Validate required fields
    if (!pointData.username) {
      throw new ValidationError('Username is required')
        .addValidationError('username', 'Username is required');
    }

    if (!pointData.firebaseUid) {
      throw new ValidationError('Firebase UID is required')
        .addValidationError('firebaseUid', 'Firebase UID is required');
    }

    if (!pointData.communityId) {
      throw new ValidationError('Community ID is required')
        .addValidationError('communityId', 'Community ID is required');
    }

    if (!pointData.email) {
      throw new ValidationError('Email is required')
        .addValidationError('email', 'Email is required');
    }

    // Check if community exists
    const community = await Community.findOne({ CommunityId: pointData.communityId });
    if (!community) {
      throw new NotFoundError('Community', pointData.communityId);
    }

    // Check if user with this Firebase UID already has a point
    const existingPoint = await Point.findOne({ firebaseUid: pointData.firebaseUid });
    if (existingPoint) {
      throw new ValidationError(`User already has a point with ID ${existingPoint.PointId}`);
    }

    // Generate a unique point ID using username and last 3 chars of Firebase UID
    const uidSuffix = pointData.firebaseUid.slice(-3);
    const pointId = `${pointData.username}${uidSuffix}`;

    // Check if point ID is already taken
    const existingPointId = await Point.findOne({ PointId: pointId });
    if (existingPointId) {
      throw new ValidationError(`Point ID ${pointId} is already taken`);
    }

    // Create the point
    const point = await Point.create({
      PointId: pointId,
      Username: pointData.username,
      CommunityId: pointData.communityId,
      firebaseUid: pointData.firebaseUid,
      email: pointData.email,
      PhoneNumber: pointData.phoneNumber || "set by user in create account",
      Position: {
        x: 0,
        y: 0,
        z: 0
      },
      metadata: "telnyx media url 1, file type(video, image, etc), file size",
      PreviewFrame: "telnyx url , last updated"
    }).catch(err => {
      throw new DatabaseError(`Failed to create point: ${err.message}`).setOriginalError(err);
    });

    // Create initial data record for this point
    await Data.create({
      PointId: pointId,
      TotalData: 50.0,
      DataAvailable: 50.0,
      DataUsed: 0.0,
      AmountPaid: 0.00,
      NextPurchase: 5.00
    }).catch(err => {
      // If data creation fails, delete the point to maintain consistency
      Point.deleteOne({ PointId: pointId }).catch(deleteErr => {
        console.error('Failed to delete point after data creation failure:', deleteErr);
      });
      throw new DatabaseError(`Failed to create data record: ${err.message}`).setOriginalError(err);
    });

    return point;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to create point: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Get all points for a community
 *
 * @param {string} communityId - Community ID
 * @param {string} firebaseUid - Firebase UID of the requester
 * @returns {Promise<Array>} List of points
 */
const getPointsByCommunityId = async (communityId, firebaseUid) => {
  try {
    if (!communityId) {
      throw new ValidationError('Community ID is required')
        .addValidationError('communityId', 'Community ID is required');
    }

    if (!firebaseUid) {
      throw new ValidationError('Firebase UID is required')
        .addValidationError('firebaseUid', 'Firebase UID is required');
    }

    // Check if user has permission to view this community
    const community = await Community.findOne({ CommunityId: communityId }).catch(err => {
      throw new DatabaseError(`Failed to fetch community: ${err.message}`).setOriginalError(err);
    });

    if (!community) {
      throw new NotFoundError('Community', communityId);
    }

    // If community is private, check if user is a member
    if (community.settings === 'Private') {
      // Get user's point
      const user = await User.findOne({ firebaseUid }).catch(err => {
        throw new DatabaseError(`Failed to fetch user: ${err.message}`).setOriginalError(err);
      });

      if (!user) {
        throw new NotFoundError('User', `with Firebase UID ${firebaseUid}`);
      }

      // Check if user is a member
      const isMember = community.members.some(member => member.pointId === user.pointId);

      if (!isMember) {
        throw new AuthorizationError('You do not have permission to view points in this community');
      }
    }

    // Get all points for this community
    const points = await Point.find({ CommunityId: communityId }).catch(err => {
      throw new DatabaseError(`Failed to fetch points: ${err.message}`).setOriginalError(err);
    });

    return points;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof AuthorizationError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to get points: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Get public points for a community (no auth required)
 *
 * @param {string} communityId - Community ID
 * @returns {Promise<Array>} List of points
 */
const getPublicPointsByCommunityId = async (communityId) => {
  try {
    if (!communityId) {
      throw new ValidationError('Community ID is required')
        .addValidationError('communityId', 'Community ID is required');
    }

    // Check if community is public
    const community = await Community.findOne({
      CommunityId: communityId,
      settings: 'Public'
    }).catch(err => {
      throw new DatabaseError(`Failed to fetch community: ${err.message}`).setOriginalError(err);
    });

    if (!community) {
      throw new NotFoundError('Public Community', communityId);
    }

    // Get all points for this community
    const points = await Point.find({ CommunityId: communityId }).catch(err => {
      throw new DatabaseError(`Failed to fetch points: ${err.message}`).setOriginalError(err);
    });

    return points;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to get public points: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Check if user has permission to add points to a community
 *
 * @param {string} firebaseUid - Firebase UID
 * @param {string} communityId - Community ID
 * @returns {Promise<boolean>} Whether user has permission
 */
const checkUserPermission = async (firebaseUid, communityId) => {
  try {
    const community = await Community.findOne({ CommunityId: communityId });
    if (!community) return false;

    // Get user's point
    const user = await User.findOne({ firebaseUid });
    if (!user) return false;

    // Get all members of the community
    const members = community.members.split(',').map(member => member.trim());

    // Check if user is a member
    return members.includes(user.pointId);
  } catch (error) {
    return false;
  }
};

/**
 * Check if user has permission to edit a specific point
 *
 * @param {string} firebaseUid - Firebase UID
 * @param {string} pointId - Point ID
 * @returns {Promise<boolean>} Whether user has permission
 */
const checkPointEditPermission = async (firebaseUid, pointId) => {
  try {
    if (!firebaseUid || !pointId) {
      throw new ValidationError('Firebase UID and Point ID are required');
    }

    // Get the point
    const point = await Point.findOne({ PointId: pointId }).catch(err => {
      throw new DatabaseError(`Failed to fetch point: ${err.message}`).setOriginalError(err);
    });

    if (!point) {
      throw new NotFoundError('Point', pointId);
    }

    // Get the user
    const user = await User.findOne({ firebaseUid }).catch(err => {
      throw new DatabaseError(`Failed to fetch user: ${err.message}`).setOriginalError(err);
    });

    if (!user) {
      throw new NotFoundError('User', `with Firebase UID ${firebaseUid}`);
    }

    // Check if user owns this point
    if (point.firebaseUid === firebaseUid) return true;

    // Check if user is founder or admin of the community
    const community = await Community.findOne({ CommunityId: point.CommunityId }).catch(err => {
      throw new DatabaseError(`Failed to fetch community: ${err.message}`).setOriginalError(err);
    });

    if (!community) {
      throw new NotFoundError('Community', point.CommunityId);
    }

    // Check if user is a member with role 'Founder' or 'Admin'
    const adminMember = community.members.find(
      member => member.firebaseUid === firebaseUid &&
                ['Founder', 'Admin'].includes(member.role)
    );

    return !!adminMember;
  } catch (error) {
    console.error('Error checking point edit permission:', error);
    return false;
  }
};

/**
 * Update a point
 *
 * @param {string} pointId - Point ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Updated point
 */
const updatePoint = async (pointId, updateData) => {
  try {
    if (!pointId) {
      throw new ValidationError('Point ID is required')
        .addValidationError('pointId', 'Point ID is required');
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      throw new ValidationError('Update data is required')
        .addValidationError('updateData', 'At least one field to update is required');
    }

    // Validate position if provided
    if (updateData.position) {
      if (typeof updateData.position !== 'object' ||
          typeof updateData.position.x !== 'number' ||
          typeof updateData.position.y !== 'number' ||
          typeof updateData.position.z !== 'number') {
        throw new ValidationError('Invalid position format')
          .addValidationError('position', 'Position must be an object with numeric x, y, z coordinates');
      }
    }

    const updateFields = {};

    if (updateData.position) {
      updateFields.Position = {
        x: updateData.position.x,
        y: updateData.position.y,
        z: updateData.position.z
      };
    }

    if (updateData.metadata) {
      updateFields.metadata = updateData.metadata;
    }

    if (updateData.previewFrame) {
      updateFields.PreviewFrame = updateData.previewFrame;
    }

    // Always update last modified
    updateFields.LastModified = new Date();

    // Check if point exists before updating
    const existingPoint = await Point.findOne({ PointId: pointId }).catch(err => {
      throw new DatabaseError(`Failed to fetch point: ${err.message}`).setOriginalError(err);
    });

    if (!existingPoint) {
      throw new NotFoundError('Point', pointId);
    }

    const updatedPoint = await Point.findOneAndUpdate(
      { PointId: pointId },
      { $set: updateFields },
      { new: true }
    ).catch(err => {
      throw new DatabaseError(`Failed to update point: ${err.message}`).setOriginalError(err);
    });

    return updatedPoint;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to update point: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Delete a point
 *
 * @param {string} pointId - Point ID
 * @returns {Promise<boolean>} Whether deletion was successful
 */
const deletePoint = async (pointId) => {
  try {
    if (!pointId) {
      throw new ValidationError('Point ID is required')
        .addValidationError('pointId', 'Point ID is required');
    }

    // Check if point exists before deleting
    const existingPoint = await Point.findOne({ PointId: pointId }).catch(err => {
      throw new DatabaseError(`Failed to fetch point: ${err.message}`).setOriginalError(err);
    });

    if (!existingPoint) {
      throw new NotFoundError('Point', pointId);
    }

    // Delete the point
    const result = await Point.deleteOne({ PointId: pointId }).catch(err => {
      throw new DatabaseError(`Failed to delete point: ${err.message}`).setOriginalError(err);
    });

    if (result.deletedCount === 0) {
      throw new DatabaseError(`Failed to delete point: No documents were deleted`);
    }

    // Also delete associated data record
    await Data.deleteOne({ PointId: pointId }).catch(err => {
      console.error(`Failed to delete data record for point ${pointId}:`, err);
      // Continue even if data deletion fails
    });

    return true;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to delete point: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Get a point by ID
 *
 * @param {string} pointId - Point ID
 * @returns {Promise<Object>} Point
 */
const getPointById = async (pointId) => {
  try {
    if (!pointId) {
      throw new ValidationError('Point ID is required')
        .addValidationError('pointId', 'Point ID is required');
    }

    const point = await Point.findOne({ PointId: pointId }).catch(err => {
      throw new DatabaseError(`Failed to fetch point: ${err.message}`).setOriginalError(err);
    });

    if (!point) {
      throw new NotFoundError('Point', pointId);
    }

    return point;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to get point: ${error.message}`).setOriginalError(error);
  }
};

/**
 * Get a point by Firebase UID
 *
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Object>} Point
 */
const getPointByFirebaseUid = async (firebaseUid) => {
  try {
    if (!firebaseUid) {
      throw new ValidationError('Firebase UID is required')
        .addValidationError('firebaseUid', 'Firebase UID is required');
    }

    const point = await Point.findOne({ firebaseUid }).catch(err => {
      throw new DatabaseError(`Failed to fetch point: ${err.message}`).setOriginalError(err);
    });

    if (!point) {
      throw new NotFoundError('Point', `with Firebase UID ${firebaseUid}`);
    }

    return point;
  } catch (error) {
    if (error instanceof ValidationError ||
        error instanceof NotFoundError ||
        error instanceof DatabaseError) {
      throw error;
    }
    throw new DatabaseError(`Failed to get point: ${error.message}`).setOriginalError(error);
  }
};

module.exports = {
  createPoint,
  getPointById,
  getPointByFirebaseUid,
  getPointsByCommunityId,
  getPublicPointsByCommunityId,
  checkUserPermission,
  checkPointEditPermission,
  updatePoint,
  deletePoint
};