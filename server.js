/**
 * Homara Backend Server
 *
 * This is the main entry point for the Homara backend application.
 * It initializes the Express server, connects to MongoDB, and sets up middleware.
 *
 * <AUTHOR> Name
 * @version 1.0.0
 */

// Load environment variables
require('dotenv').config();

// Import dependencies
const express = require('express');
const helmet = require('helmet');
const morgan = require('morgan');
const app = require('./src/app');
const mongoDBConnection = require('./src/MongoDB/MongoDBConnection');

// Constants
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Connect to MongoDB and start the server
let server;

// Initialize MongoDB connection
mongoDBConnection.connect()
  .then(result => {
    if (result.success) {
      // Start the server after successful MongoDB connection
      server = app.listen(PORT, () => {
        console.log(`Server running in ${NODE_ENV} mode on port ${PORT}`);
      });
    } else {
      console.error('Failed to connect to MongoDB. Server not started.');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Error during MongoDB connection:', err);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION! 💥 Shutting down...');
  console.error(err.name, err.message);

  // Close server & exit process
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.error(err.name, err.message);
  process.exit(1);
});

// Graceful shutdown for SIGTERM
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM RECEIVED. Shutting down gracefully');

  if (server) {
    server.close(async () => {
      // Close MongoDB connection
      await mongoDBConnection.close();
      console.log('💥 Process terminated!');
      process.exit(0);
    });
  } else {
    // If server wasn't started, just close MongoDB and exit
    mongoDBConnection.close()
      .then(() => {
        console.log('💥 Process terminated!');
        process.exit(0);
      })
      .catch(err => {
        console.error('Error closing MongoDB connection:', err);
        process.exit(1);
      });
  }
});

module.exports = server; // Export for testing
