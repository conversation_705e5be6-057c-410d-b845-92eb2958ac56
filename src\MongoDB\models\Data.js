const mongoose = require('mongoose');
const { Schema } = mongoose;

const dataSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  AmountPaid: {
    type: Number,
    default: 0.00,
    get: v => parseFloat(v).toFixed(2),
    set: v => parseFloat(v)
  },
  DataAvailable: {
    type: Number,
    default: 1.0, // GB
    comment: "Never hit zero, blocks upload",
    get: v => `${v.toFixed(2)} GB`,
    set: v => typeof v === 'string' ? parseFloat(v.split(' ')[0]) : parseFloat(v)
  },
  DataUsed: {
    type: Number,
    default: 0.0, // GB
    get: v => `${v.toFixed(2)} GB`,
    set: v => typeof v === 'string' ? parseFloat(v.split(' ')[0]) : parseFloat(v)
  },
  LastPaymentID: {
    type: String,
    default: "",
    comment: "From Stripe"
  },
  NextPurchase: {
    type: Number,
    default: 5.00,
    get: v => parseFloat(v).toFixed(2),
    set: v => parseFloat(v)
  },
  PointId: {
    type: String,
    default: ""
  },
  TotalData: {
    type: Number,
    default: 50.0, // GB
    get: v => `${v.toFixed(2)} GB`,
    set: v => typeof v === 'string' ? parseFloat(v.split(' ')[0]) : parseFloat(v)
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Index on common queries
dataSchema.index({ PointId: 1 });
dataSchema.index({ LastPaymentID: 1 });

module.exports = mongoose.model('Data', dataSchema);
