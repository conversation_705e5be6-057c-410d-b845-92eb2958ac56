/**
 * Payment Routes
 *
 * This module provides routes for payment management.
 * It includes routes for creating, processing, and retrieving payments.
 */

const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');
const paymentController = require('../../MongoDB/controllers/paymentController');
const { authenticate } = require('../../middleware/auth');
const { paymentValidationRules, validate } = require('../../middleware/validation');

// All payment routes require authentication
router.use(authenticate);

/**
 * @route   POST /api/payment/create
 * @desc    Create a new payment
 * @access  Private
 */
router.post('/create', paymentValidationRules, validate, async (req, res, next) => {
  try {
    const { amount, type, stripePaymentId } = req.body;

    const payment = await paymentController.createPayment({
      firebaseUid: req.user.uid,
      amount,
      type,
      stripePaymentId
    });

    res.status(201).json(payment);
  } catch (error) {
    next(error);
  }
});

/**
 * @route   PUT /api/payment/process/:paymentId
 * @desc    Process a payment (mark as completed or failed)
 * @access  Private
 */
router.put('/process/:paymentId', [
  param('paymentId').isString().withMessage('Payment ID is required'),
  body('status').isIn(['completed', 'failed']).withMessage('Status must be either completed or failed')
], validate, async (req, res, next) => {
  try {
    const { status } = req.body;

    const payment = await paymentController.processPayment(
      req.params.paymentId,
      status
    );

    res.json(payment);
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/payment/user
 * @desc    Get all payments for the current user
 * @access  Private
 */
router.get('/user', async (req, res, next) => {
  try {
    const payments = await paymentController.getUserPayments(req.user.uid);
    res.json(payments);
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/payment/community/:communityId
 * @desc    Get all payments for a community
 * @access  Private
 */
router.get('/community/:communityId', [
  param('communityId').isString().withMessage('Community ID is required')
], validate, async (req, res, next) => {
  try {
    const payments = await paymentController.getCommunityPayments(
      req.params.communityId
    );
    res.json(payments);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
