const Community = require('../models/Community');
const User = require('../models/User');
const Point = require('../models/Point');
const { generateUniqueId } = require('../../utils/idGenerator');

/**
 * Create a new community
 *
 * @param {Object} communityData - Community data
 * @param {string} communityData.ownerUid - Firebase UID of the founder
 * @param {string} communityData.name - Community name
 * @param {string} communityData.settings - Community settings (Public/Private)
 * @returns {Promise<Object>} Created community
 */
const createCommunity = async (communityData) => {
  try {
    // Get user's info from Firebase UID
    const user = await User.findOne({ firebaseUid: communityData.ownerUid });
    if (!user) throw new Error('User not found');

    // Generate a unique community ID using username and ordered string
    const communityId = `${user.username}@1a`;

    // Create the community
    const community = await Community.create({
      CommunityId: communityId,
      name: communityData.name,
      FounderUsername: user.username,
      settings: communityData.settings || "Private",
      members: [{
        pointId: user.pointId,
        firebaseUid: user.firebaseUid,
        username: user.username,
        role: 'Founder',
        joinedAt: new Date()
      }],
      subscriptionRenewalDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      subscriptionStatus: "Active",
      totalDuesAmount: 0.00,
      CommunityShape: "Tree"
    });

    // Update user with community ID (if they don't already have one)
    if (!user.communityId) {
      await User.updateOne(
        { _id: user._id },
        { communityId }
      );
    }

    // Update user's point with community ID
    await Point.updateOne(
      { PointId: user.pointId },
      { CommunityId: communityId }
    );

    // Update user's membership type to Founder
    await User.updateOne(
      { _id: user._id },
      { memberType: "Founder" }
    );

    // Update point's membership type to Founder
    await Point.updateOne(
      { PointId: user.pointId },
      { memberTypes: "Founder" }
    );

    return community;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all communities a user is a member of
 *
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Array>} List of communities
 */
const getUserCommunities = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');

    // Find all communities where user is a member
    const communities = await Community.find({
      'members.pointId': user.pointId
    });

    return communities;
  } catch (error) {
    throw error;
  }
};

/**
 * Get a community by ID (with auth check)
 *
 * @param {string} communityId - Community ID
 * @param {string} firebaseUid - Firebase UID of the requester
 * @returns {Promise<Object>} Community
 */
const getCommunityById = async (communityId, firebaseUid) => {
  try {
    const community = await Community.findOne({ CommunityId: communityId });

    if (!community) {
      throw new Error('Community not found');
    }

    // If community is private, check if user is a member
    if (community.settings === 'Private') {
      // Get user's pointId
      const user = await User.findOne({ firebaseUid });
      if (!user) throw new Error('User not found');

      // Check if user is a member
      const isMember = community.members.some(member => member.pointId === user.pointId);

      if (!isMember) {
        throw new Error('You do not have permission to view this community');
      }
    }

    return community;
  } catch (error) {
    throw error;
  }
};

/**
 * Get a public community by ID (no auth required)
 *
 * @param {string} communityId - Community ID
 * @returns {Promise<Object>} Community
 */
const getPublicCommunityById = async (communityId) => {
  try {
    const community = await Community.findOne({
      CommunityId: communityId,
      settings: 'Public'
    });

    if (!community) {
      throw new Error('Community not found or is not public');
    }

    return community;
  } catch (error) {
    throw error;
  }
};

/**
 * Add a member to a community
 *
 * @param {string} communityId - Community ID
 * @param {string} userPointId - Point ID of the user to add
 * @param {string} firebaseUid - Firebase UID of the user to add
 * @returns {Promise<Object>} Updated community
 */
const addCommunityMember = async (communityId, userPointId, firebaseUid) => {
  try {
    const community = await Community.findOne({ CommunityId: communityId });
    if (!community) throw new Error('Community not found');

    // Check if user is already a member
    const isMember = community.members.some(member => member.pointId === userPointId);

    if (isMember) {
      throw new Error('User is already a member of this community');
    }

    // Get user details
    const user = await User.findOne({ pointId: userPointId });
    if (!user) throw new Error('User not found');

    // Add member to community
    community.members.push({
      pointId: userPointId,
      firebaseUid: user.firebaseUid,
      username: user.username,
      role: 'Member',
      joinedAt: new Date()
    });

    await community.save();

    // Update user's community ID
    await User.updateOne(
      { pointId: userPointId },
      { communityId }
    );

    // Update point's community ID
    await Point.updateOne(
      { PointId: userPointId },
      { CommunityId: communityId }
    );

    return community;
  } catch (error) {
    throw error;
  }
};

/**
 * Check if a user is the founder of a community
 *
 * @param {string} firebaseUid - Firebase UID
 * @param {string} communityId - Community ID
 * @returns {Promise<boolean>} Whether user is the founder
 */
const isUserFounderOfCommunity = async (firebaseUid, communityId) => {
  try {
    // Get community
    const community = await Community.findOne({ CommunityId: communityId });
    if (!community) return false;

    // Check if user is a member with role 'Founder'
    const founderMember = community.members.find(
      member => member.firebaseUid === firebaseUid && member.role === 'Founder'
    );

    return !!founderMember;
  } catch (error) {
    return false;
  }
};

/**
 * Check if a user is an admin of a community
 *
 * @param {string} firebaseUid - Firebase UID
 * @param {string} communityId - Community ID
 * @returns {Promise<boolean>} Whether user is an admin
 */
const isUserAdminOfCommunity = async (firebaseUid, communityId) => {
  try {
    // Get community
    const community = await Community.findOne({ CommunityId: communityId });
    if (!community) return false;

    // Check if user is a member with role 'Founder' or 'Admin'
    const adminMember = community.members.find(
      member => member.firebaseUid === firebaseUid &&
                ['Founder', 'Admin'].includes(member.role)
    );

    return !!adminMember;
  } catch (error) {
    return false;
  }
};

/**
 * Get community by name
 *
 * @param {string} name - Community name
 * @returns {Promise<Object>} Community
 */
const getCommunityByName = async (name) => {
  try {
    const community = await Community.findOne({ name });
    return community;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createCommunity,
  getUserCommunities,
  getCommunityById,
  getPublicCommunityById,
  addCommunityMember,
  isUserFounderOfCommunity,
  isUserAdminOfCommunity,
  getCommunityByName
};