/**
 * Firebase Authentication Utilities
 *
 * This module provides utilities for Firebase authentication operations.
 * It includes functions for user management, token verification, and role-based access control.
 *
 * @version 1.0.0
 */

const admin = require('./FirebaseAdmin');
const { FirebaseAuthError } = require('./FirebaseErrors');

/**
 * Create a new Firebase user
 *
 * @param {Object} userData - User data
 * @param {string} userData.email - User email
 * @param {string} userData.password - User password
 * @param {string} [userData.displayName] - User display name
 * @param {boolean} [userData.emailVerified=false] - Whether the email is verified
 * @returns {Promise<Object>} Firebase user record
 * @throws {FirebaseAuthError} If user creation fails
 */
const createFirebaseUser = async (userData) => {
  try {
    const userRecord = await admin.auth().createUser({
      email: userData.email,
      password: userData.password,
      displayName: userData.displayName || null,
      emailVerified: userData.emailVerified || false
    });
    
    return userRecord;
  } catch (error) {
    throw new FirebaseAuthError('Failed to create Firebase user', error);
  }
};

/**
 * Verify a Firebase ID token
 *
 * @param {string} token - Firebase ID token
 * @returns {Promise<Object>} Decoded token with user information
 * @throws {FirebaseAuthError} If token verification fails
 */
const verifyIdToken = async (token) => {
  try {
    return await admin.auth().verifyIdToken(token);
  } catch (error) {
    throw new FirebaseAuthError('Invalid or expired token', error);
  }
};

/**
 * Get a user by email
 *
 * @param {string} email - User email
 * @returns {Promise<Object>} Firebase user record
 * @throws {FirebaseAuthError} If user retrieval fails
 */
const getUserByEmail = async (email) => {
  try {
    return await admin.auth().getUserByEmail(email);
  } catch (error) {
    throw new FirebaseAuthError('User not found', error);
  }
};

/**
 * Get a user by UID
 *
 * @param {string} uid - Firebase user UID
 * @returns {Promise<Object>} Firebase user record
 * @throws {FirebaseAuthError} If user retrieval fails
 */
const getUserByUid = async (uid) => {
  try {
    return await admin.auth().getUser(uid);
  } catch (error) {
    throw new FirebaseAuthError('User not found', error);
  }
};

/**
 * Update a Firebase user
 *
 * @param {string} uid - Firebase user UID
 * @param {Object} userData - User data to update
 * @returns {Promise<Object>} Updated Firebase user record
 * @throws {FirebaseAuthError} If user update fails
 */
const updateUser = async (uid, userData) => {
  try {
    return await admin.auth().updateUser(uid, userData);
  } catch (error) {
    throw new FirebaseAuthError('Failed to update user', error);
  }
};

/**
 * Delete a Firebase user
 *
 * @param {string} uid - Firebase user UID
 * @returns {Promise<void>}
 * @throws {FirebaseAuthError} If user deletion fails
 */
const deleteUser = async (uid) => {
  try {
    await admin.auth().deleteUser(uid);
  } catch (error) {
    throw new FirebaseAuthError('Failed to delete user', error);
  }
};

/**
 * Set custom claims for a user (used for role-based access control)
 *
 * @param {string} uid - Firebase user UID
 * @param {Object} claims - Custom claims to set
 * @returns {Promise<void>}
 * @throws {FirebaseAuthError} If setting claims fails
 */
const setCustomUserClaims = async (uid, claims) => {
  try {
    await admin.auth().setCustomUserClaims(uid, claims);
  } catch (error) {
    throw new FirebaseAuthError('Failed to set custom claims', error);
  }
};

/**
 * Generate a password reset link
 *
 * @param {string} email - User email
 * @returns {Promise<string>} Password reset link
 * @throws {FirebaseAuthError} If link generation fails
 */
const generatePasswordResetLink = async (email) => {
  try {
    return await admin.auth().generatePasswordResetLink(email);
  } catch (error) {
    throw new FirebaseAuthError('Failed to generate password reset link', error);
  }
};

/**
 * Generate an email verification link
 *
 * @param {string} email - User email
 * @returns {Promise<string>} Email verification link
 * @throws {FirebaseAuthError} If link generation fails
 */
const generateEmailVerificationLink = async (email) => {
  try {
    return await admin.auth().generateEmailVerificationLink(email);
  } catch (error) {
    throw new FirebaseAuthError('Failed to generate email verification link', error);
  }
};

module.exports = {
  createFirebaseUser,
  verifyIdToken,
  getUserByEmail,
  getUserByUid,
  updateUser,
  deleteUser,
  setCustomUserClaims,
  generatePasswordResetLink,
  generateEmailVerificationLink
};
