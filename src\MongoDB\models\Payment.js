const mongoose = require('mongoose');
const { Schema } = mongoose;

const paymentSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  CommunityId: {
    type: String,
    required: true
  },
  PaymentId: {
    type: String,
    required: true,
    comment: "From Stripe"
  },
  PointId: {
    type: String,
    required: true
  },
  ProcessedAt: {
    type: Date,
    default: null
  },
  RequestedAt: {
    type: Date,
    default: Date.now
  },
  amount: {
    type: Number,
    required: true,
    get: v => parseFloat(v).toFixed(2),
    set: v => parseFloat(v)
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed"],
    default: "pending"
  },
  type: {
    type: String,
    enum: ["subscription", "data-upgrade", "dues"],
    default: "subscription"
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true },
  timestamps: true
});

// Virtual for formatted dates
paymentSchema.virtual('formattedProcessedAt').get(function() {
  if (!this.ProcessedAt) return '';
  const date = this.ProcessedAt;
  return `${date.getMonth()+1}/${date.getDate()}/${date.getFullYear()}, ${date.getHours()}:${date.getMinutes()} ${date.getHours() >= 12 ? 'pm' : 'am'}`;
});

paymentSchema.virtual('formattedRequestedAt').get(function() {
  const date = this.RequestedAt;
  return `${date.getMonth()+1}/${date.getDate()}/${date.getFullYear()}, ${date.getHours()}:${date.getMinutes()} ${date.getHours() >= 12 ? 'pm' : 'am'}`;
});

// Index on common queries
paymentSchema.index({ PaymentId: 1 });
paymentSchema.index({ CommunityId: 1 });
paymentSchema.index({ PointId: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ type: 1 });

module.exports = mongoose.model('Payment', paymentSchema);
