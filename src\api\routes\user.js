/**
 * User Routes
 *
 * This module provides routes for user management and profile access.
 * It includes routes for accessing user profiles by ID.
 */

const express = require('express');
const router = express.Router();
const userController = require('../../MongoDB/controllers/userController');
const { authenticate, requireAdmin } = require('../../middleware/auth');

/**
 * @route   GET /api/user/profile/:firebaseUid
 * @desc    Get user profile by Firebase UID (admin only)
 * @access  Private/Admin
 */
router.get('/profile/:firebaseUid', authenticate, requireAdmin, async (req, res) => {
  try {
    const user = await userController.getUserByFirebaseUid(req.params.firebaseUid);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'The requested user profile was not found'
      });
    }
    
    // Don't send sensitive information
    const userProfile = {
      username: user.username,
      pointId: user.pointId,
      communityId: user.communityId,
      displayName: user.displayName,
      pointPosition: user.pointPosition,
      profile: user.profile,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      isActive: user.isActive,
      isVerified: user.isVerified
    };
    
    res.status(200).json(userProfile);
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({
      error: 'Failed to retrieve profile',
      message: error.message
    });
  }
});

module.exports = router;
