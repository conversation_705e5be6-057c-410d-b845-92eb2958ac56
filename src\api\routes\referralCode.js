/**
 * Referral Code Routes
 *
 * This module provides routes for referral code management.
 * It includes routes for creating, using, and retrieving referral codes.
 */

const express = require('express');
const router = express.Router();
const referralCodeController = require('../../MongoDB/controllers/referralCodeController');
const { authenticate } = require('../../middleware/auth');

// All referral code routes require authentication
router.use(authenticate);

/**
 * @route   POST /api/referral-code/create
 * @desc    Create a new referral code
 * @access  Private
 */
router.post('/create', async (req, res) => {
  try {
    const { communityId } = req.body;
    
    if (!communityId) {
      return res.status(400).json({ error: 'Community ID is required' });
    }
    
    const referralCode = await referralCodeController.createReferralCode({
      firebaseUid: req.user.uid,
      communityId
    });
    
    res.status(201).json(referralCode);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   GET /api/referral-code/user
 * @desc    Get all referral codes for the current user
 * @access  Private
 */
router.get('/user', async (req, res) => {
  try {
    const referralCodes = await referralCodeController.getUserReferralCodes(req.user.uid);
    res.json(referralCodes);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   POST /api/referral-code/use
 * @desc    Use a referral code
 * @access  Private
 */
router.post('/use', async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({ error: 'Referral code is required' });
    }
    
    const referralCode = await referralCodeController.useReferralCode(
      code,
      req.user.uid
    );
    
    res.json(referralCode);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/referral-code/deactivate/:code
 * @desc    Deactivate a referral code
 * @access  Private
 */
router.put('/deactivate/:code', async (req, res) => {
  try {
    const referralCode = await referralCodeController.deactivateReferralCode(
      req.params.code,
      req.user.uid
    );
    
    res.json(referralCode);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
