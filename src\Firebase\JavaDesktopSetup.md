# Firebase Setup for Java Desktop Application

This guide provides instructions for integrating Firebase Authentication into your Java desktop application.

## Prerequisites

- Java 8 or higher
- Maven or Gradle build system
- Firebase project created at [https://console.firebase.google.com/](https://console.firebase.google.com/)

## Step 1: Add Firebase SDK Dependencies

### Maven

Add the following dependencies to your `pom.xml` file:

```xml
<dependencies>
    <!-- Firebase Authentication -->
    <dependency>
        <groupId>com.google.firebase</groupId>
        <artifactId>firebase-admin</artifactId>
        <version>9.1.1</version>
    </dependency>
    
    <!-- Firebase Auth -->
    <dependency>
        <groupId>com.google.firebase</groupId>
        <artifactId>firebase-auth</artifactId>
        <version>22.0.0</version>
    </dependency>
    
    <!-- Google Auth Library -->
    <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>1.16.0</version>
    </dependency>
</dependencies>
```

### Gradle

Add the following dependencies to your `build.gradle` file:

```gradle
dependencies {
    // Firebase Authentication
    implementation 'com.google.firebase:firebase-admin:9.1.1'
    implementation 'com.google.firebase:firebase-auth:22.0.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.16.0'
}
```

## Step 2: Initialize Firebase in Your Java Application

Create a Firebase initialization class:

```java
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class FirebaseInitializer {
    private static FirebaseApp firebaseApp;

    public static void initialize() {
        try {
            // Load service account credentials
            InputStream serviceAccount = new FileInputStream("path/to/firebase-service-account.json");
            
            FirebaseOptions options = FirebaseOptions.builder()
                .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                .setDatabaseUrl("https://your-project-id.firebaseio.com")
                .build();
            
            // Initialize Firebase
            firebaseApp = FirebaseApp.initializeApp(options);
            System.out.println("Firebase initialized successfully");
        } catch (IOException e) {
            System.err.println("Error initializing Firebase: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static FirebaseAuth getAuth() {
        if (firebaseApp == null) {
            initialize();
        }
        return FirebaseAuth.getInstance();
    }
}
```

## Step 3: Implement Authentication Methods

Create an authentication service class:

```java
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.google.firebase.auth.UserRecord;

public class FirebaseAuthService {
    private final FirebaseAuth auth;

    public FirebaseAuthService() {
        this.auth = FirebaseInitializer.getAuth();
    }

    /**
     * Verify a Firebase ID token
     */
    public FirebaseToken verifyIdToken(String idToken) throws FirebaseAuthException {
        return auth.verifyIdToken(idToken);
    }

    /**
     * Get user by UID
     */
    public UserRecord getUserByUid(String uid) throws FirebaseAuthException {
        return auth.getUser(uid);
    }

    /**
     * Get user by email
     */
    public UserRecord getUserByEmail(String email) throws FirebaseAuthException {
        return auth.getUserByEmail(email);
    }

    /**
     * Create a new user
     */
    public UserRecord createUser(String email, String password, String displayName) throws FirebaseAuthException {
        UserRecord.CreateRequest request = new UserRecord.CreateRequest()
            .setEmail(email)
            .setPassword(password)
            .setDisplayName(displayName)
            .setEmailVerified(false);
        
        return auth.createUser(request);
    }

    /**
     * Update a user
     */
    public UserRecord updateUser(String uid, String displayName) throws FirebaseAuthException {
        UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(uid)
            .setDisplayName(displayName);
        
        return auth.updateUser(request);
    }

    /**
     * Delete a user
     */
    public void deleteUser(String uid) throws FirebaseAuthException {
        auth.deleteUser(uid);
    }
}
```

## Step 4: Implement User Authentication UI

Create a login form in your Java application that collects user credentials and sends them to your backend API for authentication.

Example login form using JavaFX:

```java
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.stage.Stage;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class LoginForm extends Application {
    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Login");
        
        GridPane grid = new GridPane();
        grid.setPadding(new Insets(20, 20, 20, 20));
        grid.setVgap(10);
        grid.setHgap(10);
        
        Label emailLabel = new Label("Email:");
        TextField emailField = new TextField();
        emailField.setPromptText("Enter your email");
        
        Label passwordLabel = new Label("Password:");
        PasswordField passwordField = new PasswordField();
        passwordField.setPromptText("Enter your password");
        
        Button loginButton = new Button("Login");
        Label messageLabel = new Label();
        
        grid.add(emailLabel, 0, 0);
        grid.add(emailField, 1, 0);
        grid.add(passwordLabel, 0, 1);
        grid.add(passwordField, 1, 1);
        grid.add(loginButton, 1, 2);
        grid.add(messageLabel, 1, 3);
        
        loginButton.setOnAction(e -> {
            String email = emailField.getText();
            String password = passwordField.getText();
            
            try {
                // Send login request to your backend API
                String token = login(email, password);
                messageLabel.setText("Login successful!");
                
                // Store the token for future API requests
                // Navigate to main application screen
                
            } catch (Exception ex) {
                messageLabel.setText("Login failed: " + ex.getMessage());
            }
        });
        
        Scene scene = new Scene(grid, 400, 200);
        primaryStage.setScene(scene);
        primaryStage.show();
    }
    
    private String login(String email, String password) throws Exception {
        URL url = new URL("http://localhost:3000/api/auth/login");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);
        
        String jsonInput = String.format("{\"email\":\"%s\",\"password\":\"%s\"}", email, password);
        
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonInput.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
        }
        
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            
            // Parse the response to get the token
            // This is a simplified example - you should use a JSON parser
            String responseStr = response.toString();
            int tokenStart = responseStr.indexOf("\"token\":\"") + 9;
            int tokenEnd = responseStr.indexOf("\"", tokenStart);
            return responseStr.substring(tokenStart, tokenEnd);
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
```

## Step 5: Secure API Requests

For all subsequent API requests from your desktop app to your backend, include the Firebase ID token in the Authorization header:

```java
private void makeApiRequest(String endpoint, String token) throws Exception {
    URL url = new URL("http://localhost:3000/api/" + endpoint);
    HttpURLConnection conn = (HttpURLConnection) url.openConnection();
    conn.setRequestMethod("GET");
    conn.setRequestProperty("Authorization", "Bearer " + token);
    
    // Process the response
    // ...
}
```

## Security Considerations

1. **Never hardcode Firebase credentials** in your desktop application
2. **Use a backend API** for sensitive operations instead of direct Firebase calls
3. **Implement token refresh** logic to handle expired tokens
4. **Validate all user input** before sending to the backend
5. **Implement proper error handling** for authentication failures
