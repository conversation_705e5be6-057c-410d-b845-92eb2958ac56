/**
 * Firebase Admin SDK Initialization
 *
 * This module initializes the Firebase Admin SDK for server-side operations
 * like authentication verification, user management, etc.
 *
 * The Firebase Admin SDK provides server-side functionality for:
 * - Verifying ID tokens from clients
 * - Managing users (create, update, delete)
 * - Custom claims for role-based access control
 * - Server-side data validation and security
 *
 * SETUP INSTRUCTIONS:
 * 1. Create a Firebase project at https://console.firebase.google.com/
 * 2. Generate a service account key from Project Settings > Service Accounts
 * 3. Save the key as firebase-service-account.json in the project root
 * 4. For production, encode the key as base64 and set as FIREBASE_SERVICE_ACCOUNT_BASE64
 *
 * @version 1.0.0
 */

const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Initialize Firebase Admin SDK
let firebaseApp;

try {
  // In production, use environment variables
  if (process.env.FIREBASE_SERVICE_ACCOUNT_BASE64) {
    try {
      // Decode base64-encoded service account from environment variable
      const serviceAccountJson = Buffer.from(
        process.env.FIREBASE_SERVICE_ACCOUNT_BASE64,
        'base64'
      ).toString('utf8');

      const serviceAccount = JSON.parse(serviceAccountJson);

      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET
      });

      console.log('Firebase Admin SDK initialized with environment variables');
    } catch (decodeError) {
      console.error('Failed to decode Firebase service account:', decodeError);
      throw new Error('Invalid FIREBASE_SERVICE_ACCOUNT_BASE64 environment variable');
    }
  }
  // In development, use local service account file
  else {
    const serviceAccountPath = path.resolve(
      __dirname,
      '../../firebase-service-account.json'
    );

    // Check if service account file exists
    if (!fs.existsSync(serviceAccountPath)) {
      console.error(
        'Firebase service account file not found at:',
        serviceAccountPath
      );
      console.error(
        'Please create this file or set FIREBASE_SERVICE_ACCOUNT_BASE64 environment variable'
      );
      console.error(
        'See src/Firebase/README.md for setup instructions'
      );
    } else {
      try {
        const serviceAccount = require(serviceAccountPath);

        firebaseApp = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          databaseURL: process.env.FIREBASE_DATABASE_URL || 'https://your-firebase-app.firebaseio.com',
          storageBucket: process.env.FIREBASE_STORAGE_BUCKET
        });

        console.log('Firebase Admin SDK initialized with local service account file');
      } catch (fileError) {
        console.error('Failed to load Firebase service account file:', fileError);
        throw new Error('Invalid firebase-service-account.json file');
      }
    }
  }
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);

  // In development, create a mock Firebase admin for testing
  if (process.env.NODE_ENV === 'development') {
    console.warn('Using mock Firebase Admin SDK for development');
    console.warn('This should ONLY be used for local development and testing');

    // Create a mock auth object with common Firebase Auth methods
    const mockAuth = {
      verifyIdToken: async (token) => {
        // For development, accept any token and return a mock user
        console.warn('DEVELOPMENT MODE: Using mock Firebase authentication');
        return {
          uid: 'dev-user-123',
          email: '<EMAIL>',
          name: 'Development User',
          email_verified: true
        };
      },
      createUser: async (userData) => {
        console.warn('DEVELOPMENT MODE: Creating mock Firebase user');
        return {
          uid: 'new-user-' + Date.now(),
          email: userData.email,
          emailVerified: false,
          displayName: userData.displayName || null
        };
      },
      updateUser: async (uid, userData) => {
        console.warn('DEVELOPMENT MODE: Updating mock Firebase user:', uid);
        return {
          uid,
          ...userData
        };
      },
      getUserByEmail: async (email) => {
        console.warn('DEVELOPMENT MODE: Getting mock Firebase user by email:', email);
        return {
          uid: 'dev-user-123',
          email,
          emailVerified: true,
          displayName: 'Development User'
        };
      },
      setCustomUserClaims: async (uid, claims) => {
        console.warn('DEVELOPMENT MODE: Setting custom claims for user:', uid, claims);
        return;
      },
      deleteUser: async (uid) => {
        console.warn('DEVELOPMENT MODE: Deleting mock Firebase user:', uid);
        return;
      }
    };

    // Create a mock admin object
    admin.auth = () => mockAuth;
  }
}

module.exports = admin;