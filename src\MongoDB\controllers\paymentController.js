/**
 * Payment Controller
 *
 * This module provides functions for managing payments.
 * It handles payment creation, processing, and retrieval.
 */

const Payment = require('../models/Payment');
const User = require('../models/User');
const Point = require('../models/Point');
const Data = require('../models/Data');
const { generateUniqueId } = require('../../utils/idGenerator');

/**
 * Create a new payment record
 * 
 * @param {Object} paymentData - Payment data
 * @param {string} paymentData.firebaseUid - Firebase UID of the user
 * @param {string} paymentData.amount - Payment amount
 * @param {string} paymentData.type - Payment type (subscription, data-upgrade, dues)
 * @param {string} paymentData.stripePaymentId - Stripe payment ID
 * @returns {Promise<Object>} Created payment record
 */
const createPayment = async (paymentData) => {
  try {
    // Get user's pointId and communityId from Firebase UID
    const user = await User.findOne({ firebaseUid: paymentData.firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Create the payment record
    const payment = await Payment.create({
      PaymentId: paymentData.stripePaymentId || `(From Stripe)`,
      CommunityId: user.communityId,
      PointId: user.pointId,
      amount: paymentData.amount,
      type: paymentData.type,
      status: "pending",
      ProcessedAt: "",
      RequestedAt: new Date().toLocaleString('en-US', {
        month: 'numeric',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      })
    });
    
    // Update user's last payment ID
    user.lastPaymentId = payment.PaymentId;
    await user.save();
    
    // If this is a data upgrade payment, update the Data record
    if (paymentData.type === "data-upgrade") {
      let dataRecord = await Data.findOne({ PointId: user.pointId });
      
      // Create data record if it doesn't exist
      if (!dataRecord) {
        dataRecord = await Data.create({
          PointId: user.pointId,
          LastPaymentID: payment.PaymentId,
          AmountPaid: paymentData.amount
        });
      } else {
        // Update existing data record
        dataRecord.LastPaymentID = payment.PaymentId;
        dataRecord.AmountPaid = paymentData.amount;
        await dataRecord.save();
      }
    }
    
    return payment;
  } catch (error) {
    throw error;
  }
};

/**
 * Process a payment (mark as completed or failed)
 * 
 * @param {string} paymentId - Payment ID
 * @param {string} status - New status (completed or failed)
 * @returns {Promise<Object>} Updated payment record
 */
const processPayment = async (paymentId, status) => {
  try {
    // Find the payment
    const payment = await Payment.findOne({ PaymentId: paymentId });
    if (!payment) throw new Error('Payment not found');
    
    // Update payment status
    payment.status = status;
    payment.ProcessedAt = new Date().toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
    
    await payment.save();
    
    // If payment is completed and it's a data upgrade, update the data limits
    if (status === "completed" && payment.type === "data-upgrade") {
      const dataRecord = await Data.findOne({ PointId: payment.PointId });
      if (dataRecord) {
        // Parse current data and add new amount (simple string parsing for example)
        const currentData = parseInt(dataRecord.TotalData.split(' ')[0]) || 50;
        const newData = currentData + 10; // Add 10GB for each upgrade
        
        dataRecord.TotalData = `${newData} GB`;
        dataRecord.DataAvailable = `${newData - parseInt(dataRecord.DataUsed.split(' ')[0])} GB`;
        
        await dataRecord.save();
        
        // Also update the point record
        const point = await Point.findOne({ PointId: payment.PointId });
        if (point) {
          point.dataLimit = `${newData}GB`;
          await point.save();
        }
      }
    }
    
    return payment;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all payments for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Array>} List of payments
 */
const getUserPayments = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get all payments for this user
    const payments = await Payment.find({ PointId: user.pointId });
    return payments;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all payments for a community
 * 
 * @param {string} communityId - Community ID
 * @returns {Promise<Array>} List of payments
 */
const getCommunityPayments = async (communityId) => {
  try {
    // Get all payments for this community
    const payments = await Payment.find({ CommunityId: communityId });
    return payments;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createPayment,
  processPayment,
  getUserPayments,
  getCommunityPayments
};
