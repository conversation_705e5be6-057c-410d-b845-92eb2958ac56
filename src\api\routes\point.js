const express = require('express');
const router = express.Router();
const pointController = require('../../MongoDB/controllers/pointController');
const { authenticate } = require('../../middleware/auth');
const { pointValidationRules, validate } = require('../../middleware/validation');

// All point routes require authentication
router.use(authenticate);

// Get all points for a community
router.get('/community/:communityId', async (req, res) => {
  try {
    const points = await pointController.getPointsByCommunityId(
      req.params.communityId, 
      req.user.uid
    );
    res.json(points);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Create new point
router.post(
  '/community/:communityId',
  pointValidationRules,
  validate,
  async (req, res) => {
    try {
      // Check if user has permission to add points to this community
      const hasPermission = await pointController.checkUserPermission(
        req.user.uid,
        req.params.communityId
      );
      
      if (!hasPermission) {
        return res.status(403).json({ 
          error: 'You do not have permission to add points to this community' 
        });
      }
      
      const point = await pointController.createPoint({
        communityId: req.params.communityId,
        creatorId: req.user.uid,
        position: req.body.position,
        metadata: req.body.metadata || {}
      });
      
      res.status(201).json(point);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
);

// Update point
router.put(
  '/:pointId',
  pointValidationRules,
  validate,
  async (req, res) => {
    try {
      // Check if user has permission to edit this point
      const hasPermission = await pointController.checkPointEditPermission(
        req.user.uid,
        req.params.pointId
      );
      
      if (!hasPermission) {
        return res.status(403).json({ 
          error: 'You do not have permission to edit this point' 
        });
      }
      
      const updatedPoint = await pointController.updatePoint(
        req.params.pointId,
        {
          position: req.body.position,
          metadata: req.body.metadata
        }
      );
      
      res.json(updatedPoint);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
);

// Delete point
router.delete('/:pointId', async (req, res) => {
  try {
    // Check if user has permission to delete this point
    const hasPermission = await pointController.checkPointEditPermission(
      req.user.uid,
      req.params.pointId
    );
    
    if (!hasPermission) {
      return res.status(403).json({ 
        error: 'You do not have permission to delete this point' 
      });
    }
    
    await pointController.deletePoint(req.params.pointId);
    res.json({ message: 'Point deleted successfully' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;