/**
 * Referral Code Controller
 *
 * This module provides functions for managing referral codes.
 * It handles code creation, validation, and usage.
 */

const ReferralCode = require('../models/ReferralCode');
const User = require('../models/User');
const { generateUniqueId } = require('../../utils/idGenerator');

/**
 * Create a new referral code
 * 
 * @param {Object} codeData - Referral code data
 * @param {string} codeData.firebaseUid - Firebase UID of the creator
 * @param {string} codeData.communityId - Community ID
 * @returns {Promise<Object>} Created referral code
 */
const createReferralCode = async (codeData) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid: codeData.firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Generate a unique code
    const code = generateUniqueId('REF').substring(0, 8);
    
    // Create the referral code
    const referralCode = await ReferralCode.create({
      Code: code,
      CommunityId: codeData.communityId,
      PointId: user.pointId,
      Active: "True",
      UsedBy: ""
    });
    
    return referralCode;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all referral codes for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Array>} List of referral codes
 */
const getUserReferralCodes = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get all referral codes for this user
    const referralCodes = await ReferralCode.find({ PointId: user.pointId });
    return referralCodes;
  } catch (error) {
    throw error;
  }
};

/**
 * Validate and use a referral code
 * 
 * @param {string} code - Referral code to validate
 * @param {string} firebaseUid - Firebase UID of the user using the code
 * @returns {Promise<Object>} Used referral code
 */
const useReferralCode = async (code, firebaseUid) => {
  try {
    // Find the referral code
    const referralCode = await ReferralCode.findOne({ Code: code, Active: "True" });
    if (!referralCode) throw new Error('Invalid or inactive referral code');
    
    // Check if code is already used
    if (referralCode.UsedBy) throw new Error('Referral code has already been used');
    
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Update the referral code
    referralCode.UsedBy = user.pointId;
    referralCode.Active = "False";
    await referralCode.save();
    
    // Update user's referral code
    user.referralCode = code;
    await user.save();
    
    return referralCode;
  } catch (error) {
    throw error;
  }
};

/**
 * Deactivate a referral code
 * 
 * @param {string} code - Referral code to deactivate
 * @param {string} firebaseUid - Firebase UID of the code owner
 * @returns {Promise<Object>} Deactivated referral code
 */
const deactivateReferralCode = async (code, firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Find the referral code
    const referralCode = await ReferralCode.findOne({ Code: code, PointId: user.pointId });
    if (!referralCode) throw new Error('Referral code not found or does not belong to this user');
    
    // Deactivate the code
    referralCode.Active = "False";
    await referralCode.save();
    
    return referralCode;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createReferralCode,
  getUserReferralCodes,
  useReferralCode,
  deactivateReferralCode
};
