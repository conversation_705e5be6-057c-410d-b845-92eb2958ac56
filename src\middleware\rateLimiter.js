/**
 * Rate limiting middleware for the Homara backend
 *
 * This file contains rate limiting configurations for different API endpoints
 * to prevent abuse and ensure fair usage of the API.
 *
 * @version 1.0.0
 */

const rateLimit = require('express-rate-limit');

// Get configuration from environment variables or use defaults
const PUBLIC_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000; // 15 minutes
const PUBLIC_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100;

/**
 * Rate limiter for public viewing endpoints
 * Less strict - allows more requests
 */
const publicViewRateLimit = rateLimit({
  windowMs: PUBLIC_WINDOW_MS,
  max: PUBLIC_MAX_REQUESTS,
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many requests, please try again later'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

/**
 * Rate limiter for account creation
 * Very strict - prevents mass account creation
 */
const accountCreationRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 account creations per hour
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many accounts created from this IP, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Rate limiter for login attempts
 * Strict - prevents brute force attacks
 */
const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 login attempts per 15 minutes
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many login attempts, please try again after 15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Rate limiter for API endpoints that modify data
 * Medium strictness - balances security and usability
 */
const apiWriteRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 30, // Limit each IP to 30 write operations per 5 minutes
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many write operations, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Rate limiter for password reset and email verification
 * Strict - prevents abuse of email services
 */
const emailActionRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 email actions per hour
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many email requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

module.exports = {
  publicViewRateLimit,
  accountCreationRateLimit,
  loginRateLimit,
  apiWriteRateLimit,
  emailActionRateLimit
};
