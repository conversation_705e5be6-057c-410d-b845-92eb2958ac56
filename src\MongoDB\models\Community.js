const mongoose = require('mongoose');
const { Schema } = mongoose;

// Define the member schema
const memberSchema = new Schema({
  pointId: {
    type: String,
    required: true
  },
  firebaseUid: {
    type: String,
    required: true
  },
  username: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['Founder', 'Admin', 'Member'],
    default: 'Member'
  },
  joinedAt: {
    type: Date,
    default: Date.now
  }
});

const communitySchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  CommunityId: {
    type: String,
    required: true,
    unique: true,
    comment: "username + ordered string (@1a cus first, @2a,@3a)"
  },
  members: [memberSchema],
  name: {
    type: String,
    required: true
  },
  settings: {
    type: String,
    enum: ["Public", "Private"],
    default: "Private"
  },
  subscriptionRenewalDate: {
    type: Date,
    default: () => {
      const date = new Date();
      date.setFullYear(date.getFullYear() + 1);
      return date;
    }
  },
  subscriptionStatus: {
    type: String,
    enum: ["Active", "Expired"],
    default: "Active"
  },
  totalDuesAmount: {
    type: Number,
    default: 0.00,
    get: v => `$${parseFloat(v).toFixed(2)}`,
    set: v => typeof v === 'string' ? parseFloat(v.replace('$', '')) : parseFloat(v)
  },
  FounderUsername: {
    type: String,
    required: true
  },
  CommunityShape: {
    type: String,
    enum: ["Tree", "Circle", "Grid"],
    default: "Tree"
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true },
  timestamps: true
});

// Virtual for formatted dates
communitySchema.virtual('formattedSubscriptionRenewalDate').get(function() {
  return this.subscriptionRenewalDate.toLocaleDateString('en-US');
});

// Index on common queries
communitySchema.index({ CommunityId: 1 });
communitySchema.index({ FounderUsername: 1 });
communitySchema.index({ subscriptionStatus: 1 });
communitySchema.index({ 'members.firebaseUid': 1 });
communitySchema.index({ 'members.pointId': 1 });

module.exports = mongoose.model('Community', communitySchema);