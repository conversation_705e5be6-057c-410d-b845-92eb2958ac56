const mongoose = require('mongoose');
const { Schema } = mongoose;

const pointSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  Username: {
    type: String,
    required: true,
    comment: "set by user in create account"
  },
  CommunityId: {
    type: String,
    required: true
  },
  PointId: {
    type: String,
    required: true,
    unique: true,
    comment: "username + last 3 of firebase ID"
  },
  dataLimit: {
    type: Number,
    default: 50.0, // GB
    get: v => `${v.toFixed(2)}GB`,
    set: v => typeof v === 'string' ? parseFloat(v.replace('GB', '')) : parseFloat(v)
  },
  dataUsage: {
    type: Number,
    default: 0.0, // GB
    get: v => `${v.toFixed(2)}GB`,
    set: v => typeof v === 'string' ? parseFloat(v.replace('GB', '')) : parseFloat(v)
  },
  firebaseUid: {
    type: String,
    required: true
  },
  referralCode: {
    type: String,
    default: "N/A"
  },
  LastPaymentID: {
    type: String,
    comment: "from stripe"
  },
  email: {
    type: String,
    required: true
  },
  memberTypes: {
    type: String,
    enum: ["Founder", "Member"],
    default: "Member"
  },
  PhoneNumber: {
    type: String,
    comment: "set by user in create account"
  },
  Position: {
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 }
  },
  metadata: {
    type: String,
    default: "telnyx media url 1, file type(video, image, etc), file size"
  },
  LastModified: {
    type: Date,
    default: Date.now
  },
  PreviewFrame: {
    type: String,
    default: "telnyx url , last updated"
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true },
  timestamps: true
});

// Virtual for formatted date
pointSchema.virtual('formattedLastModified').get(function() {
  const date = this.LastModified;
  return `${date.getMonth()+1}/${date.getDate()}/${date.getFullYear()}, ${date.getHours()}:${date.getMinutes()} ${date.getHours() >= 12 ? 'pm' : 'am'}`;
});

// Index on common queries
pointSchema.index({ PointId: 1 });
pointSchema.index({ CommunityId: 1 });
pointSchema.index({ Username: 1 });
pointSchema.index({ firebaseUid: 1 });

module.exports = mongoose.model('Point', pointSchema);