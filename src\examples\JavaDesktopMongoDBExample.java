/**
 * MongoDB Integration Example for Java Desktop Application
 * 
 * This example demonstrates how to interact with the MongoDB backend
 * through the REST API endpoints. It shows how to:
 * 1. Authenticate with Firebase
 * 2. Fetch user profile data from MongoDB
 * 3. Update user data in MongoDB
 * 4. Update point position in MongoDB
 */

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.HashMap;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class JavaDesktopMongoDBExample {
    
    private static final String API_BASE_URL = "http://localhost:3000/api";
    private static final Gson gson = new Gson();
    private String idToken;
    
    // User data model
    public static class UserProfile {
        public String userId;
        public String username;
        public String email;
        public String displayName;
        public String pointId;
        public String communityId;
        public PointPosition pointPosition;
        public boolean isVerified;
        public String lastLogin;
        
        @Override
        public String toString() {
            return "UserProfile{" +
                    "userId='" + userId + '\'' +
                    ", username='" + username + '\'' +
                    ", email='" + email + '\'' +
                    ", pointId='" + pointId + '\'' +
                    ", communityId='" + communityId + '\'' +
                    ", pointPosition=" + pointPosition +
                    '}';
        }
    }
    
    // Point position model
    public static class PointPosition {
        public double x;
        public double y;
        public double z;
        
        public PointPosition(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        @Override
        public String toString() {
            return "PointPosition{" +
                    "x=" + x +
                    ", y=" + y +
                    ", z=" + z +
                    '}';
        }
    }
    
    /**
     * Initialize Firebase and authenticate the user
     * 
     * @param email User email
     * @param password User password
     * @return Firebase ID token
     * @throws Exception If authentication fails
     */
    public String authenticateWithFirebase(String email, String password) throws Exception {
        // This is a simplified example. In a real application, you would use
        // the Firebase Authentication SDK to sign in the user and get a token.
        // For this example, we'll simulate the process.
        
        URL url = new URL("https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=YOUR_API_KEY");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);
        
        String jsonInput = String.format("{\"email\":\"%s\",\"password\":\"%s\",\"returnSecureToken\":true}", 
                email, password);
        
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonInput.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Authentication failed: HTTP error code: " + conn.getResponseCode());
        }
        
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            
            JsonObject jsonResponse = JsonParser.parseString(response.toString()).getAsJsonObject();
            this.idToken = jsonResponse.get("idToken").getAsString();
            return this.idToken;
        }
    }
    
    /**
     * Fetch user profile from MongoDB backend
     * 
     * @return User profile data
     * @throws Exception If fetching fails
     */
    public UserProfile fetchUserProfile() throws Exception {
        if (idToken == null) {
            throw new IllegalStateException("User not authenticated. Call authenticateWithFirebase first.");
        }
        
        URL url = new URL(API_BASE_URL + "/auth/desktop-profile");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Authorization", "Bearer " + idToken);
        
        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed to fetch profile: HTTP error code: " + conn.getResponseCode());
        }
        
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            
            return gson.fromJson(response.toString(), UserProfile.class);
        }
    }
    
    /**
     * Update user's point position in MongoDB
     * 
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return Updated point position
     * @throws Exception If update fails
     */
    public PointPosition updatePointPosition(double x, double y, double z) throws Exception {
        if (idToken == null) {
            throw new IllegalStateException("User not authenticated. Call authenticateWithFirebase first.");
        }
        
        URL url = new URL(API_BASE_URL + "/auth/point-position");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("PUT");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Authorization", "Bearer " + idToken);
        conn.setDoOutput(true);
        
        String jsonInput = String.format("{\"x\":%f,\"y\":%f,\"z\":%f}", x, y, z);
        
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonInput.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed to update position: HTTP error code: " + conn.getResponseCode());
        }
        
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            
            JsonObject jsonResponse = JsonParser.parseString(response.toString()).getAsJsonObject();
            JsonObject position = jsonResponse.getAsJsonObject("pointPosition");
            
            return new PointPosition(
                position.get("x").getAsDouble(),
                position.get("y").getAsDouble(),
                position.get("z").getAsDouble()
            );
        }
    }
    
    /**
     * Example usage
     */
    public static void main(String[] args) {
        try {
            JavaDesktopMongoDBExample example = new JavaDesktopMongoDBExample();
            
            // Step 1: Authenticate with Firebase
            String token = example.authenticateWithFirebase("<EMAIL>", "password123");
            System.out.println("Authentication successful! Token: " + token.substring(0, 10) + "...");
            
            // Step 2: Fetch user profile from MongoDB
            UserProfile profile = example.fetchUserProfile();
            System.out.println("User profile fetched: " + profile);
            
            // Step 3: Update user's point position
            PointPosition updatedPosition = example.updatePointPosition(10.5, 20.3, 30.7);
            System.out.println("Position updated: " + updatedPosition);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
