/**
 * User Model
 *
 * This model represents a user in the system. It stores user information
 * that is linked to Firebase Authentication via the firebaseUid field.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;

const userSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  // Firebase Authentication UID
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  // User's chosen username
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },

  // Unique identifier for the user's point in the system
  pointId: {
    type: String,
    required: true,
    unique: true,
    index: true,
    comment: "username + last 3 of firebase ID"
  },

  // Community the user belongs to (optional)
  communityId: {
    type: String,
    index: true
  },

  // User's email from Firebase (for easy reference)
  email: {
    type: String,
    required: true,
    sparse: true,
    index: true
  },

  // User's phone number
  phoneNumber: {
    type: String,
    comment: "set by user in create account"
  },

  // User's membership type
  memberType: {
    type: String,
    enum: ["Founder", "Member"],
    default: "Member"
  },

  // User's referral code
  referralCode: {
    type: String,
    default: "N/A"
  },

  // User's last payment ID
  lastPaymentId: {
    type: String,
    comment: "from stripe"
  },

  // Data limits and usage
  dataLimit: {
    type: Number,
    default: 50.0, // GB
    get: v => `${v.toFixed(2)}GB`,
    set: v => typeof v === 'string' ? parseFloat(v.replace('GB', '')) : parseFloat(v)
  },

  dataUsage: {
    type: Number,
    default: 0.0, // GB
    get: v => `${v.toFixed(2)}GB`,
    set: v => typeof v === 'string' ? parseFloat(v.replace('GB', '')) : parseFloat(v)
  },

  // User's profile information
  profile: {
    bio: { type: String, maxlength: 500 },
    avatarUrl: { type: String },
    preferences: {
      theme: { type: String, enum: ['light', 'dark', 'system'], default: 'system' },
      notifications: { type: Boolean, default: true }
    }
  },

  // User's point position in 3D space (if applicable)
  pointPosition: {
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 }
  },

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },

  // Account status
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true },
  timestamps: true
});

// Create indexes for common queries
userSchema.index({ username: 1 });
userSchema.index({ pointId: 1 });
userSchema.index({ communityId: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ referralCode: 1 });

module.exports = mongoose.model('User', userSchema);