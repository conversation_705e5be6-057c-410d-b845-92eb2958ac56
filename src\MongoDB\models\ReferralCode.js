const mongoose = require('mongoose');
const { Schema } = mongoose;

const referralCodeSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  Active: {
    type: String,
    enum: ["True", "False"],
    default: "True"
  },
  Code: {
    type: String,
    required: true,
    unique: true
  },
  CommunityId: {
    type: String,
    required: true
  },
  PointId: {
    type: String,
    required: true,
    comment: "point ID created the code"
  },
  UsedBy: {
    type: String,
    default: "",
    comment: "PointID using the code"
  }
});

// Index on common queries
referralCodeSchema.index({ Code: 1 });
referralCodeSchema.index({ CommunityId: 1 });
referralCodeSchema.index({ PointId: 1 });
referralCodeSchema.index({ UsedBy: 1 });

module.exports = mongoose.model('ReferralCode', referralCodeSchema);
