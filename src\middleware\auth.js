/**
 * Authentication Middleware
 *
 * This module provides middleware functions for authentication and authorization.
 * It uses Firebase for authentication and custom claims for role-based access control.
 */

const { verifyIdToken } = require('../Firebase/FirebaseAuth');
const { getHttpStatusFromFirebaseError, getClientMessageFromFirebaseError } = require('../Firebase/FirebaseErrors');

/**
 * Middleware to verify Firebase authentication
 *
 * This middleware extracts the Firebase ID token from the Authorization header,
 * verifies it, and attaches the decoded user information to the request object.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const authenticate = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'No valid authentication token provided'
      });
    }

    const token = authHeader.split('Bearer ')[1];
    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'No valid authentication token provided'
      });
    }

    // Verify the token
    const decodedToken = await verifyIdToken(token);

    // Attach user info to request object
    req.user = decodedToken;

    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    // Get appropriate status code and message from the error
    const status = getHttpStatusFromFirebaseError(error) || 401;
    const message = getClientMessageFromFirebaseError(error) || 'Authentication failed';

    // Log the error (but don't expose sensitive details to the client)
    console.error('Authentication error:', error);

    // Return error response
    return res.status(status).json({
      error: 'Authentication failed',
      message
    });
  }
};

/**
 * Middleware to check if user has admin role
 *
 * This middleware checks if the authenticated user has the admin role
 * based on custom claims in the Firebase ID token.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const requireAdmin = (req, res, next) => {
  // This middleware should be used after the authenticate middleware
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'You must be authenticated to access this resource'
    });
  }

  // Check if user has admin role
  if (!req.user.admin) {
    return res.status(403).json({
      error: 'Permission denied',
      message: 'You do not have permission to access this resource'
    });
  }

  // User is an admin, continue
  next();
};

module.exports = {
  authenticate,
  requireAdmin
};