/**
 * Data Routes
 *
 * This module provides routes for data management.
 * It includes routes for retrieving and updating data usage and limits.
 */

const express = require('express');
const router = express.Router();
const dataController = require('../../MongoDB/controllers/dataController');
const { authenticate } = require('../../middleware/auth');

// All data routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/data/user
 * @desc    Get data record for the current user
 * @access  Private
 */
router.get('/user', async (req, res) => {
  try {
    const dataRecord = await dataController.getUserDataRecord(req.user.uid);
    res.json(dataRecord);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/data/usage
 * @desc    Update data usage
 * @access  Private
 */
router.put('/usage', async (req, res) => {
  try {
    const { usedBytes } = req.body;
    
    if (typeof usedBytes !== 'number' || usedBytes < 0) {
      return res.status(400).json({ error: 'Invalid data usage' });
    }
    
    const dataRecord = await dataController.updateDataUsage(
      req.user.uid,
      usedBytes
    );
    
    res.json(dataRecord);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/data/limit
 * @desc    Increase data limit
 * @access  Private
 */
router.put('/limit', async (req, res) => {
  try {
    const { additionalGB, paymentId } = req.body;
    
    if (typeof additionalGB !== 'number' || additionalGB <= 0) {
      return res.status(400).json({ error: 'Invalid data limit increase' });
    }
    
    const dataRecord = await dataController.increaseDataLimit(
      req.user.uid,
      additionalGB,
      paymentId
    );
    
    res.json(dataRecord);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
