/**
 * MongoDB Connection Module for Homara
 *
 * This module provides a connection to MongoDB and methods for interacting with the database.
 * It is designed to be used in a Node.js environment.
 *
 * <AUTHOR> Name
 * @version 1.0.0
 */

const mongoose = require('mongoose');
const { EventEmitter } = require('events');

class MongoDBConnection extends EventEmitter {
    constructor() {
        super();
        this.isConnected = false;
        this.connectionString = process.env.MONGODB_URI || "mongodb://localhost:27017/homara";

        // Configure mongoose
        mongoose.set('strictQuery', false);
    }

    /**
     * Initialize MongoDB connection
     *
     * @returns {Promise<Object>} Connection result
     */
    async connect() {
        if (this.isConnected) return { success: true };

        try {
            console.log('Connecting to MongoDB Atlas...');

            // MongoDB Atlas connection options
            const options = {
                useNewUrlParser: true,
                useUnifiedTopology: true,
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                connectTimeoutMS: 30000,
                retryWrites: true,
                w: 'majority'
            };

            await mongoose.connect(this.connectionString, options);

            this.isConnected = true;
            console.log('Connected to MongoDB Atlas successfully');

            // Set up connection event listeners
            mongoose.connection.on('error', (err) => {
                console.error('MongoDB connection error:', err);
                this.isConnected = false;
                this.emit('error', err);
            });

            mongoose.connection.on('disconnected', () => {
                console.log('MongoDB disconnected');
                this.isConnected = false;
                this.emit('disconnected');

                // Attempt to reconnect after a delay
                setTimeout(() => {
                    if (!this.isConnected) {
                        console.log('Attempting to reconnect to MongoDB...');
                        this.connect();
                    }
                }, 5000);
            });

            // Handle process termination
            process.on('SIGINT', this.close.bind(this));

            this.emit('connected');
            return { success: true };
        } catch (error) {
            console.error('Error connecting to MongoDB Atlas:', error);
            this.emit('error', error);
            return { success: false, error };
        }
    }

    /**
     * Close MongoDB connection
     *
     * @returns {Promise<Object>} Close result
     */
    async close() {
        if (!this.isConnected) return { success: true };

        try {
            await mongoose.connection.close();
            this.isConnected = false;
            console.log('MongoDB connection closed');
            this.emit('closed');
            return { success: true };
        } catch (error) {
            console.error('Error closing MongoDB connection:', error);
            this.emit('error', error);
            return { success: false, error };
        }
    }
}

// Create and export a singleton instance
const mongoDBConnection = new MongoDBConnection();
module.exports = mongoDBConnection;
