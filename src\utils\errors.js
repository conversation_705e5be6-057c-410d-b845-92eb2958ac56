/**
 * Custom error classes for the application
 */

// Base error class
class AppError extends Error {
  constructor(message, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

// 400 Bad Request - Invalid input
class ValidationError extends AppError {
  constructor(message = 'Validation failed') {
    super(message, 400);
    this.validationErrors = [];
  }

  addValidationError(field, message) {
    this.validationErrors.push({ field, message });
    return this;
  }
}

// 401 Unauthorized - Authentication required
class AuthenticationError extends AppError {
  constructor(message = 'Authentication required') {
    super(message, 401);
  }
}

// 403 Forbidden - Not allowed
class AuthorizationError extends AppError {
  constructor(message = 'You do not have permission to perform this action') {
    super(message, 403);
  }
}

// 404 Not Found - Resource not found
class NotFoundError extends AppError {
  constructor(resource = 'Resource', id = '') {
    const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
    super(message, 404);
  }
}

// 409 Conflict - Resource already exists
class ConflictError extends AppError {
  constructor(message = 'Resource already exists') {
    super(message, 409);
  }
}

// 429 Too Many Requests - Rate limit exceeded
class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429);
  }
}

// 500 Internal Server Error - Database error
class DatabaseError extends AppError {
  constructor(message = 'Database operation failed') {
    super(message, 500);
    this.originalError = null;
  }

  setOriginalError(error) {
    this.originalError = error;
    return this;
  }
}

// Error handler middleware
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error response
  const errorResponse = {
    error: true,
    message: err.message || 'Something went wrong',
    statusCode: err.statusCode || 500
  };

  // Add validation errors if available
  if (err instanceof ValidationError && err.validationErrors.length > 0) {
    errorResponse.validationErrors = err.validationErrors;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
    if (err instanceof DatabaseError && err.originalError) {
      errorResponse.originalError = {
        message: err.originalError.message,
        stack: err.originalError.stack
      };
    }
  }

  res.status(errorResponse.statusCode).json(errorResponse);
};

module.exports = {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  errorHandler
};
