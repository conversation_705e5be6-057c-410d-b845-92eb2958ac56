/**
 * App Tests
 * 
 * Basic tests for the Express application
 */

const request = require('supertest');
const app = require('../src/app');

describe('App', () => {
  // Test health check endpoint
  describe('GET /health', () => {
    it('should return 200 OK with status message', async () => {
      const response = await request(app).get('/health');
      expect(response.statusCode).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('message', 'Server is running');
    });
  });

  // Test 404 handling
  describe('404 Handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app).get('/non-existent-route');
      expect(response.statusCode).toBe(404);
      expect(response.body).toHaveProperty('message');
    });
  });
});
