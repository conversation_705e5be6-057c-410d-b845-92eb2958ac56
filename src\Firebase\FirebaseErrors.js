/**
 * Firebase Error Handling
 *
 * This module provides error handling utilities for Firebase operations.
 * It includes custom error classes and error mapping functions.
 *
 * @version 1.0.0
 */

/**
 * Custom error class for Firebase authentication errors
 */
class FirebaseAuthError extends Error {
  /**
   * Create a new FirebaseAuthError
   * 
   * @param {string} message - Error message
   * @param {Error} originalError - Original Firebase error
   */
  constructor(message, originalError) {
    super(message);
    this.name = 'FirebaseAuthError';
    this.code = originalError?.code || 'unknown';
    this.originalError = originalError;
    this.status = getHttpStatusFromFirebaseError(originalError);
    this.clientMessage = getClientMessageFromFirebaseError(originalError);
  }
}

/**
 * Map Firebase error codes to HTTP status codes
 * 
 * @param {Error} error - Firebase error
 * @returns {number} HTTP status code
 */
function getHttpStatusFromFirebaseError(error) {
  if (!error || !error.code) return 500;

  // Map Firebase Auth error codes to HTTP status codes
  const errorMap = {
    // Authentication errors
    'auth/email-already-exists': 409, // Conflict
    'auth/invalid-email': 400, // Bad Request
    'auth/invalid-password': 400, // Bad Request
    'auth/user-not-found': 404, // Not Found
    'auth/wrong-password': 401, // Unauthorized
    'auth/invalid-credential': 401, // Unauthorized
    'auth/user-disabled': 403, // Forbidden
    'auth/id-token-expired': 401, // Unauthorized
    'auth/id-token-revoked': 401, // Unauthorized
    'auth/invalid-id-token': 401, // Unauthorized
    'auth/session-cookie-expired': 401, // Unauthorized
    'auth/session-cookie-revoked': 401, // Unauthorized
    'auth/invalid-session-cookie': 401, // Unauthorized
    'auth/email-already-in-use': 409, // Conflict
    'auth/operation-not-allowed': 403, // Forbidden
    'auth/weak-password': 400, // Bad Request
    'auth/phone-number-already-exists': 409, // Conflict
    'auth/invalid-phone-number': 400, // Bad Request
    'auth/missing-phone-number': 400, // Bad Request
    'auth/quota-exceeded': 429, // Too Many Requests
    'auth/captcha-check-failed': 400, // Bad Request
    'auth/missing-verification-code': 400, // Bad Request
    'auth/invalid-verification-code': 400, // Bad Request
    'auth/missing-verification-id': 400, // Bad Request
    'auth/invalid-verification-id': 400, // Bad Request
    'auth/missing-multi-factor-session': 400, // Bad Request
    'auth/missing-multi-factor-info': 400, // Bad Request
    'auth/invalid-multi-factor-session': 400, // Bad Request
    'auth/multi-factor-info-not-found': 404, // Not Found
    'auth/admin-restricted-operation': 403, // Forbidden
    'auth/unverified-email': 403, // Forbidden
    'auth/tenant-id-mismatch': 400, // Bad Request
    'auth/unsupported-tenant-operation': 400, // Bad Request
    'auth/missing-android-pkg-name': 400, // Bad Request
    'auth/missing-ios-bundle-id': 400, // Bad Request
    'auth/unauthorized-domain': 403, // Forbidden
    'auth/invalid-dynamic-link-domain': 400, // Bad Request
    'auth/rejected-credential': 401, // Unauthorized
    'auth/invalid-argument': 400, // Bad Request
    'auth/invalid-claims': 400, // Bad Request
    'auth/invalid-continue-uri': 400, // Bad Request
    'auth/invalid-creation-time': 400, // Bad Request
    'auth/invalid-disabled-field': 400, // Bad Request
    'auth/invalid-display-name': 400, // Bad Request
    'auth/invalid-email-verified': 400, // Bad Request
    'auth/invalid-hash-algorithm': 400, // Bad Request
    'auth/invalid-hash-block-size': 400, // Bad Request
    'auth/invalid-hash-derived-key-length': 400, // Bad Request
    'auth/invalid-hash-key': 400, // Bad Request
    'auth/invalid-hash-memory-cost': 400, // Bad Request
    'auth/invalid-hash-parallelization': 400, // Bad Request
    'auth/invalid-hash-rounds': 400, // Bad Request
    'auth/invalid-hash-salt-separator': 400, // Bad Request
    'auth/invalid-photo-url': 400, // Bad Request
    'auth/invalid-provider-data': 400, // Bad Request
    'auth/invalid-provider-id': 400, // Bad Request
    'auth/invalid-oauth-responsetype': 400, // Bad Request
    'auth/invalid-session-cookie-duration': 400, // Bad Request
    'auth/invalid-uid': 400, // Bad Request
    'auth/maximum-user-count-exceeded': 429, // Too Many Requests
    'auth/missing-uid': 400, // Bad Request
    'auth/reserved-claims': 400, // Bad Request
    'auth/session-cookie-revoked': 401, // Unauthorized
    'auth/uid-already-exists': 409, // Conflict
    'auth/app-not-authorized': 403, // Forbidden
    'auth/invalid-api-key': 401, // Unauthorized
    'auth/network-request-failed': 500, // Internal Server Error
    'auth/requires-recent-login': 403, // Forbidden
    'auth/too-many-requests': 429, // Too Many Requests
    'auth/user-token-expired': 401, // Unauthorized
    'auth/web-storage-unsupported': 400, // Bad Request
    'auth/invalid-credential': 401, // Unauthorized
    'auth/app-deleted': 400, // Bad Request
    'auth/account-exists-with-different-credential': 409, // Conflict
    'auth/invalid-persistence-type': 400, // Bad Request
    'auth/unsupported-persistence-type': 400, // Bad Request
    'auth/user-cancelled': 400, // Bad Request
    'auth/user-mismatch': 400, // Bad Request
    'auth/user-signed-out': 401, // Unauthorized
    'auth/weak-password': 400, // Bad Request
    'auth/missing-continue-uri': 400, // Bad Request
    'auth/missing-iframe-start': 400, // Bad Request
    'auth/missing-or-invalid-nonce': 400, // Bad Request
    'auth/missing-app-credential': 400, // Bad Request
    'auth/invalid-app-credential': 400, // Bad Request
    'auth/invalid-message-payload': 400, // Bad Request
    'auth/invalid-recipient-email': 400, // Bad Request
    'auth/invalid-sender': 400, // Bad Request
    'auth/missing-action-code': 400, // Bad Request
    'auth/invalid-action-code': 400, // Bad Request
    'auth/invalid-tenant-id': 400, // Bad Request
    'auth/tenant-not-found': 404, // Not Found
    'auth/missing-tenant-id': 400, // Bad Request
    'auth/missing-oauth-client-id': 400, // Bad Request
    'auth/invalid-oauth-client-id': 400, // Bad Request
    'auth/unauthorized-continue-uri': 403, // Forbidden
    'auth/missing-app-id': 400, // Bad Request
    'auth/missing-recaptcha-token': 400, // Bad Request
    'auth/invalid-recaptcha-token': 400, // Bad Request
    'auth/invalid-recaptcha-action': 400, // Bad Request
    'auth/missing-client-type': 400, // Bad Request
    'auth/missing-recaptcha-version': 400, // Bad Request
    'auth/invalid-recaptcha-version': 400, // Bad Request
    'auth/invalid-req-type': 400, // Bad Request
  };

  return errorMap[error.code] || 500;
}

/**
 * Get a user-friendly error message from a Firebase error
 * 
 * @param {Error} error - Firebase error
 * @returns {string} User-friendly error message
 */
function getClientMessageFromFirebaseError(error) {
  if (!error || !error.code) return 'An unknown error occurred';

  // Map Firebase Auth error codes to user-friendly messages
  const messageMap = {
    // Authentication errors
    'auth/email-already-exists': 'The email address is already in use by another account.',
    'auth/invalid-email': 'The email address is not valid.',
    'auth/invalid-password': 'The password is invalid or the user does not have a password.',
    'auth/user-not-found': 'There is no user record corresponding to this identifier.',
    'auth/wrong-password': 'The password is invalid or the user does not have a password.',
    'auth/invalid-credential': 'The credential is malformed or has expired.',
    'auth/user-disabled': 'The user account has been disabled by an administrator.',
    'auth/id-token-expired': 'The user\'s credential has expired. Please sign in again.',
    'auth/id-token-revoked': 'The user\'s credential has been revoked. Please sign in again.',
    'auth/invalid-id-token': 'The user\'s credential is no longer valid. Please sign in again.',
    'auth/email-already-in-use': 'The email address is already in use by another account.',
    'auth/operation-not-allowed': 'This operation is not allowed.',
    'auth/weak-password': 'The password must be at least 6 characters long.',
    'auth/phone-number-already-exists': 'The phone number is already in use by another account.',
    'auth/invalid-phone-number': 'The phone number is not valid.',
    'auth/missing-phone-number': 'Please provide a phone number.',
    'auth/quota-exceeded': 'The quota for this operation has been exceeded. Try again later.',
    'auth/captcha-check-failed': 'The reCAPTCHA response is invalid. Please try again.',
    'auth/missing-verification-code': 'Please provide a verification code.',
    'auth/invalid-verification-code': 'The verification code is invalid.',
    'auth/missing-verification-id': 'Please provide a verification ID.',
    'auth/invalid-verification-id': 'The verification ID is invalid.',
    'auth/unverified-email': 'Please verify your email address before proceeding.',
    'auth/unauthorized-domain': 'This domain is not authorized for OAuth operations.',
    'auth/rejected-credential': 'The credential was rejected. Please try again.',
    'auth/requires-recent-login': 'This operation requires recent authentication. Please sign in again.',
    'auth/too-many-requests': 'Too many unsuccessful login attempts. Please try again later.',
    'auth/user-token-expired': 'Your session has expired. Please sign in again.',
    'auth/web-storage-unsupported': 'This browser does not support web storage. Please try another browser.',
    'auth/account-exists-with-different-credential': 'An account already exists with the same email address but different sign-in credentials.',
    'auth/user-cancelled': 'The user cancelled the sign-in process.',
    'auth/user-mismatch': 'The supplied credentials do not correspond to the previously signed in user.',
    'auth/user-signed-out': 'The user has been signed out.',
    'auth/invalid-action-code': 'The action code is invalid. This can happen if the code is malformed, expired, or has already been used.',
  };

  return messageMap[error.code] || error.message || 'An unknown error occurred';
}

module.exports = {
  FirebaseAuthError,
  getHttpStatusFromFirebaseError,
  getClientMessageFromFirebaseError
};
