/**
 * Authentication Routes
 *
 * This module provides routes for user authentication and account management.
 * It includes routes for registration, login, password reset, etc.
 */

const express = require('express');
const router = express.Router();
const userController = require('../../MongoDB/controllers/userController');
const User = require('../../MongoDB/models/User');
const { authenticate } = require('../../middleware/auth');
const { accountCreationRateLimit, loginRateLimit } = require('../../middleware/rateLimiter');
const { userValidationRules, validate } = require('../../middleware/validation');
const { getClientMessageFromFirebaseError } = require('../../Firebase/FirebaseErrors');

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post(
  '/register',
  accountCreationRateLimit,
  userValidationRules,
  validate,
  async (req, res) => {
    try {
      const { email, password, username, displayName } = req.body;

      // Check if user already exists
      const existingUser = await userController.getUserByEmail(email);
      if (existingUser) {
        return res.status(409).json({
          error: 'Registration failed',
          message: 'An account with this email already exists'
        });
      }

      // Create new user
      const newUser = await userController.createUser({
        email,
        password,
        username,
        displayName: displayName || username
      });

      // Send verification email (optional)
      try {
        await userController.sendEmailVerification(email);
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Continue with registration even if email sending fails
      }

      res.status(201).json({
        message: 'Account created successfully',
        pointId: newUser.pointId,
        username: newUser.username
      });
    } catch (error) {
      console.error('Registration error:', error);

      // Get user-friendly error message
      const message = getClientMessageFromFirebaseError(error) || error.message || 'Registration failed';

      res.status(error.status || 400).json({
        error: 'Registration failed',
        message
      });
    }
  }
);

/**
 * @route   GET /api/auth/check-username
 * @desc    Check if a username is available
 * @access  Public
 */
router.get('/check-username', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.status(400).json({
        error: 'Missing username',
        message: 'Username is required'
      });
    }

    // Check if username exists in MongoDB
    const existingUser = await User.findOne({ username });

    res.json({
      available: !existingUser,
      username
    });
  } catch (error) {
    console.error('Username check error:', error);
    res.status(500).json({
      error: 'Failed to check username',
      message: error.message
    });
  }
});

/**
 * @route   POST /api/auth/login
 * @desc    Login a user and return a token
 * @access  Public
 */
router.post('/login', loginRateLimit, async (req, res) => {
  // Note: This endpoint is just a placeholder.
  // Actual authentication happens on the client side using Firebase SDK.
  // The client will get a token directly from Firebase, which they'll use for subsequent API calls.
  res.status(400).json({
    error: 'Invalid login method',
    message: 'Please use Firebase Authentication SDK to login. This endpoint is not used.'
  });
});

/**
 * @route   POST /api/auth/password-reset
 * @desc    Send password reset email
 * @access  Public
 */
router.post('/password-reset', loginRateLimit, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        error: 'Missing email',
        message: 'Email is required'
      });
    }

    // Generate password reset link
    const resetLink = await userController.sendPasswordResetEmail(email);

    res.status(200).json({
      message: 'Password reset email sent',
      resetLink: process.env.NODE_ENV === 'development' ? resetLink : undefined
    });
  } catch (error) {
    console.error('Password reset error:', error);

    // Don't reveal if the email exists or not for security reasons
    res.status(200).json({
      message: 'If an account with that email exists, a password reset link has been sent'
    });
  }
});

/**
 * @route   POST /api/auth/send-verification
 * @desc    Send email verification link
 * @access  Public
 */
router.post('/send-verification', loginRateLimit, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        error: 'Missing email',
        message: 'Email is required'
      });
    }

    // Generate verification link
    const verificationLink = await userController.sendEmailVerification(email);

    res.status(200).json({
      message: 'Verification email sent',
      verificationLink: process.env.NODE_ENV === 'development' ? verificationLink : undefined
    });
  } catch (error) {
    console.error('Email verification error:', error);

    // Don't reveal if the email exists or not for security reasons
    res.status(200).json({
      message: 'If an account with that email exists, a verification link has been sent'
    });
  }
});

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await userController.getUserByFirebaseUid(req.user.uid);

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'Your user profile was not found'
      });
    }

    // Don't send sensitive information
    const userProfile = {
      username: user.username,
      pointId: user.pointId,
      communityId: user.communityId,
      email: req.user.email,
      displayName: user.displayName || req.user.name,
      emailVerified: req.user.email_verified,
      pointPosition: user.pointPosition,
      profile: user.profile,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      isActive: user.isActive,
      isVerified: user.isVerified,
      isAdmin: !!req.user.admin
    };

    res.status(200).json(userProfile);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to retrieve profile',
      message: error.message
    });
  }
});

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile', authenticate, async (req, res) => {
  try {
    const {
      username,
      displayName,
      bio,
      avatarUrl,
      pointPosition,
      preferences
    } = req.body;

    // Update user profile with expanded fields
    const updatedUser = await userController.updateUserProfile(req.user.uid, {
      username,
      displayName,
      bio,
      avatarUrl,
      pointPosition,
      preferences
    });

    res.status(200).json({
      message: 'Profile updated successfully',
      user: {
        username: updatedUser.username,
        pointId: updatedUser.pointId,
        displayName: updatedUser.displayName,
        pointPosition: updatedUser.pointPosition,
        profile: updatedUser.profile
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(error.status || 400).json({
      error: 'Failed to update profile',
      message: error.message
    });
  }
});

/**
 * @route   DELETE /api/auth/account
 * @desc    Delete user account
 * @access  Private
 */
router.delete('/account', authenticate, async (req, res) => {
  try {
    await userController.deleteUserAccount(req.user.uid);

    res.status(200).json({
      message: 'Account deleted successfully'
    });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to delete account',
      message: error.message
    });
  }
});

/**
 * @route   PUT /api/auth/point-position
 * @desc    Update user's point position
 * @access  Private
 */
router.put('/point-position', authenticate, async (req, res) => {
  try {
    const { x, y, z } = req.body;

    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      return res.status(400).json({
        error: 'Invalid position data',
        message: 'Position coordinates must be numbers'
      });
    }

    // Update just the point position
    const updatedUser = await userController.updateUserProfile(req.user.uid, {
      pointPosition: { x, y, z }
    });

    res.status(200).json({
      message: 'Point position updated successfully',
      pointPosition: updatedUser.pointPosition
    });
  } catch (error) {
    console.error('Update point position error:', error);
    res.status(error.status || 400).json({
      error: 'Failed to update point position',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/auth/desktop-profile
 * @desc    Get user profile data specifically formatted for desktop app
 * @access  Private
 */
router.get('/desktop-profile', authenticate, async (req, res) => {
  try {
    const user = await userController.getUserByFirebaseUid(req.user.uid);

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'Your user profile was not found'
      });
    }

    // Format data specifically for desktop app
    const desktopProfile = {
      userId: user.firebaseUid,
      username: user.username,
      email: req.user.email,
      displayName: user.displayName || req.user.name,
      pointId: user.pointId,
      communityId: user.communityId,
      pointPosition: user.pointPosition,
      isVerified: user.isVerified,
      lastLogin: user.lastLogin
    };

    res.status(200).json(desktopProfile);
  } catch (error) {
    console.error('Get desktop profile error:', error);
    res.status(500).json({
      error: 'Failed to retrieve profile',
      message: error.message
    });
  }
});

module.exports = router;