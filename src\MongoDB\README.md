# MongoDB Atlas Integration for Homara Backend

This directory contains the MongoDB integration for the Homara backend application. It provides models, controllers, and utilities for interacting with MongoDB Atlas.

## Setup Instructions

### 1. Create a MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas) and sign up for a free account
2. Create a new project (e.g., "Homara")

### 2. Create a Cluster

1. Click "Build a Database"
2. Choose the free tier option (M0)
3. Select your preferred cloud provider and region (choose a region close to your users)
4. Name your cluster (e.g., "homara-cluster")
5. Click "Create Cluster"

### 3. Set Up Database Access

1. In the left sidebar, click "Database Access"
2. Click "Add New Database User"
3. Create a username and password (use a strong password and save it securely)
4. Set user privileges to "Atlas admin" for development (use more restricted permissions for production)
5. Click "Add User"

### 4. Configure Network Access

1. In the left sidebar, click "Network Access"
2. Click "Add IP Address"
3. For development, you can add your current IP or use "0.0.0.0/0" to allow access from anywhere (not recommended for production)
4. Click "Confirm"

### 5. Get Connection String

1. In the left sidebar, click "Databases"
2. Click "Connect" on your cluster
3. Choose "Connect your application"
4. Select "Node.js" as the driver and the appropriate version
5. Copy the connection string
6. Replace `<password>` with your database user's password
7. Replace `<dbname>` with your database name (e.g., "homara")

### 6. Configure Environment Variables

Add the MongoDB Atlas connection string to your `.env` file:

```
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<dbname>?retryWrites=true&w=majority
```

## Models

The MongoDB integration includes the following models:

- `User.js` - User model for storing user information
- `Community.js` - Community model for storing community information
- `Point.js` - Point model for storing 3D point information

## Controllers

The MongoDB integration includes the following controllers:

- `userController.js` - Functions for managing users
- `communityController.js` - Functions for managing communities
- `pointController.js` - Functions for managing points

## Connection

The `MongoDBConnection.js` file provides a singleton instance for connecting to MongoDB Atlas. It handles:

- Connection establishment
- Error handling
- Reconnection logic
- Event emission

## Usage in Desktop App

The Java desktop application can interact with MongoDB through the REST API endpoints. See the `JavaDesktopMongoDBExample.java` file in the `examples` directory for a complete example.

Key endpoints for the desktop app:

- `GET /api/auth/desktop-profile` - Get user profile data
- `PUT /api/auth/point-position` - Update user's point position
- `GET /api/points/community/:communityId` - Get all points for a community

## Usage in Web App

The web application can interact with MongoDB through the REST API endpoints. Key endpoints for user creation:

- `POST /api/auth/register` - Register a new user
- `PUT /api/auth/profile` - Update user profile

## Security Considerations

1. **Never hardcode MongoDB credentials** in your application code
2. Use environment variables for sensitive information
3. Implement proper authentication and authorization
4. Use HTTPS for all API requests
5. Validate all user input before storing in MongoDB
6. Implement proper error handling
7. Use MongoDB Atlas's built-in security features (IP whitelisting, VPC peering, etc.)
8. Regularly backup your data

## Troubleshooting

If you encounter connection issues:

1. Check your MongoDB Atlas connection string
2. Verify your IP is whitelisted in Network Access
3. Check your database user credentials
4. Ensure your MongoDB Atlas cluster is running
5. Check the server logs for detailed error messages
