/**
 * Community Dues Routes
 *
 * This module provides routes for community dues management.
 * It includes routes for creating, paying, and retrieving dues.
 */

const express = require('express');
const router = express.Router();
const communityDuesController = require('../../MongoDB/controllers/communityDuesController');
const communityController = require('../../MongoDB/controllers/communityController');
const { authenticate } = require('../../middleware/auth');

// All community dues routes require authentication
router.use(authenticate);

/**
 * @route   POST /api/community-dues/create
 * @desc    Create new dues for a community member
 * @access  Private
 */
router.post('/create', async (req, res) => {
  try {
    const { communityId, pointId, amount, dueDate } = req.body;
    
    // Check if user is the founder of the community
    const isFounder = await communityController.isUserFounderOfCommunity(
      req.user.uid,
      communityId
    );
    
    if (!isFounder) {
      return res.status(403).json({
        error: 'Only the community founder can create dues'
      });
    }
    
    const dues = await communityDuesController.createDues({
      communityId,
      pointId,
      amount,
      dueDate
    });
    
    res.status(201).json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   POST /api/community-dues/create-all
 * @desc    Create dues for all members of a community
 * @access  Private
 */
router.post('/create-all', async (req, res) => {
  try {
    const { communityId, amount, dueDate } = req.body;
    
    // Check if user is the founder of the community
    const isFounder = await communityController.isUserFounderOfCommunity(
      req.user.uid,
      communityId
    );
    
    if (!isFounder) {
      return res.status(403).json({
        error: 'Only the community founder can create dues'
      });
    }
    
    const dues = await communityDuesController.createDuesForAllMembers({
      communityId,
      amount,
      dueDate
    });
    
    res.status(201).json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/community-dues/pay/:duesId
 * @desc    Pay dues
 * @access  Private
 */
router.put('/pay/:duesId', async (req, res) => {
  try {
    const { paymentId } = req.body;
    
    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }
    
    const dues = await communityDuesController.payDues(
      req.params.duesId,
      paymentId
    );
    
    res.json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   GET /api/community-dues/community/:communityId
 * @desc    Get all dues for a community
 * @access  Private
 */
router.get('/community/:communityId', async (req, res) => {
  try {
    const dues = await communityDuesController.getCommunityDues(
      req.params.communityId
    );
    res.json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   GET /api/community-dues/user
 * @desc    Get all dues for the current user
 * @access  Private
 */
router.get('/user', async (req, res) => {
  try {
    const dues = await communityDuesController.getUserDues(req.user.uid);
    res.json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * @route   GET /api/community-dues/user/pending
 * @desc    Get all pending dues for the current user
 * @access  Private
 */
router.get('/user/pending', async (req, res) => {
  try {
    const dues = await communityDuesController.getUserPendingDues(req.user.uid);
    res.json(dues);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
