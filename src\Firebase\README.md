# Firebase Integration for Homara Backend

This directory contains the Firebase integration for the Homara backend application.

## Files

- `FirebaseAdmin.js` - Server-side Firebase Admin SDK initialization
- `FirebaseConfig.js` - Firebase configuration for client applications
- `FirebaseAuth.js` - Authentication utilities for Firebase
- `FirebaseErrors.js` - Error handling utilities for Firebase

## Setup Instructions

### Development Environment

1. Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Generate a new private key for your service account:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file as `firebase-service-account.json` in the root directory of this project
3. Create a `.env` file in the root directory with the following variables:
   ```
   FIREBASE_API_KEY=your-api-key
   FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
   FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
   FIREBASE_APP_ID=your-app-id
   FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
   ```


// we did this development/environment stuff and now we are moving on

### Production Environment

For production, you should set the following environment variables:

1. `FIREBASE_SERVICE_ACCOUNT_BASE64` - Base64-encoded service account JSON
2. `FIREBASE_DATABASE_URL` - Firebase database URL
3. `FIREBASE_API_KEY` - Firebase API key
4. `FIREBASE_AUTH_DOMAIN` - Firebase auth domain
5. `FIREBASE_PROJECT_ID` - Firebase project ID
6. `FIREBASE_STORAGE_BUCKET` - Firebase storage bucket
7. `FIREBASE_MESSAGING_SENDER_ID` - Firebase messaging sender ID
8. `FIREBASE_APP_ID` - Firebase app ID

To generate the `FIREBASE_SERVICE_ACCOUNT_BASE64` value:
```bash
cat firebase-service-account.json | base64
```

## Security Considerations

- Never commit the `firebase-service-account.json` file to version control
- Use environment variables for all Firebase configuration in production
- Implement proper authentication and authorization checks
- Set up Firebase Security Rules for your database and storage
