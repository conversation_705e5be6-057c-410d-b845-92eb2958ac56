/**
 * User Controller
 *
 * This module provides functions for managing users in both Firebase and MongoDB.
 * It handles user creation, retrieval, updating, and deletion.
 */

const User = require('../models/User');
const {
  createFirebaseUser,
  getUserByEmail: getFirebaseUserByEmail,
  getUserByUid,
  updateUser,
  deleteUser,
  setCustomUserClaims,
  generatePasswordResetLink,
  generateEmailVerificationLink
} = require('../../Firebase/FirebaseAuth');
const { FirebaseAuthError } = require('../../Firebase/FirebaseErrors');
const { generateUniqueId } = require('../../utils/idGenerator');

/**
 * Create a new user in both Firebase and MongoDB
 *
 * @param {Object} userData - User data
 * @param {string} userData.email - User email
 * @param {string} userData.password - User password
 * @param {string} userData.username - User username
 * @param {string} [userData.displayName] - User display name (defaults to username)
 * @returns {Promise<Object>} Created user object
 * @throws {Error} If user creation fails
 */
const createUser = async (userData) => {
  try {
    // Create Firebase user
    const firebaseUser = await createFirebaseUser({
      email: userData.email,
      password: userData.password,
      displayName: userData.displayName || userData.username
    });

    // Generate a unique point ID for the user
    const pointId = generateUniqueId('PT');

    // Create MongoDB user with expanded fields
    const user = await User.create({
      firebaseUid: firebaseUser.uid,
      username: userData.username,
      email: userData.email,
      displayName: userData.displayName || userData.username,
      pointId,
      pointPosition: userData.pointPosition || { x: 0, y: 0, z: 0 },
      profile: {
        bio: userData.bio || '',
        avatarUrl: userData.avatarUrl || '',
        preferences: userData.preferences || {
          theme: 'system',
          notifications: true
        }
      },
      isVerified: false,
      createdAt: new Date(),
      lastLogin: new Date()
    });

    return user;
  } catch (error) {
    // If Firebase user was created but MongoDB user creation failed,
    // we should delete the Firebase user to maintain consistency
    if (error.name !== 'FirebaseAuthError' && error.firebaseUid) {
      try {
        await deleteUser(error.firebaseUid);
      } catch (deleteError) {
        console.error('Failed to delete Firebase user after MongoDB user creation failed:', deleteError);
      }
    }

    // Rethrow the original error
    throw error;
  }
};

/**
 * Get a user by their point ID
 *
 * @param {string} pointId - User point ID
 * @returns {Promise<Object|null>} User object or null if not found
 */
const getUserByPointId = async (pointId) => {
  return await User.findOne({ pointId });
};

/**
 * Get a user by their Firebase UID
 *
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Object|null>} User object or null if not found
 */
const getUserByFirebaseUid = async (firebaseUid) => {
  return await User.findOne({ firebaseUid });
};

/**
 * Get a user by their email address
 *
 * @param {string} email - User email
 * @returns {Promise<Object|null>} User object or null if not found
 * @throws {Error} If user retrieval fails
 */
const getUserByEmail = async (email) => {
  try {
    // Get Firebase user by email
    const firebaseUser = await getFirebaseUserByEmail(email);

    // Get MongoDB user by Firebase UID
    return await User.findOne({ firebaseUid: firebaseUser.uid });
  } catch (error) {
    if (error.code === 'auth/user-not-found') {
      return null;
    }
    throw error;
  }
};

/**
 * Update a user's profile
 *
 * @param {string} firebaseUid - Firebase UID
 * @param {Object} userData - User data to update
 * @returns {Promise<Object>} Updated user object
 * @throws {Error} If user update fails
 */
const updateUserProfile = async (firebaseUid, userData) => {
  try {
    // Update Firebase user if needed
    if (userData.displayName || userData.email || userData.password) {
      await updateUser(firebaseUid, {
        displayName: userData.displayName,
        email: userData.email,
        password: userData.password
      });
    }

    // Find and update MongoDB user with expanded fields
    const updateData = {
      lastLogin: new Date()
    };

    // Only update fields that are provided
    if (userData.username) updateData.username = userData.username;
    if (userData.displayName) updateData.displayName = userData.displayName;
    if (userData.email) updateData.email = userData.email;

    // Update point position if provided
    if (userData.pointPosition) {
      updateData.pointPosition = {
        x: userData.pointPosition.x || 0,
        y: userData.pointPosition.y || 0,
        z: userData.pointPosition.z || 0
      };
    }

    // Update profile fields if provided
    if (userData.bio || userData.avatarUrl || userData.preferences) {
      updateData.profile = {};

      // Get current user to preserve existing profile data
      const currentUser = await User.findOne({ firebaseUid });
      if (currentUser && currentUser.profile) {
        updateData.profile = { ...currentUser.profile };
      }

      if (userData.bio) updateData.profile.bio = userData.bio;
      if (userData.avatarUrl) updateData.profile.avatarUrl = userData.avatarUrl;

      if (userData.preferences) {
        updateData.profile.preferences = {
          ...updateData.profile.preferences,
          ...userData.preferences
        };
      }
    }

    // Update community ID if provided
    if (userData.communityId) {
      updateData.communityId = userData.communityId;
    }

    const updatedUser = await User.findOneAndUpdate(
      { firebaseUid },
      { $set: updateData },
      { new: true }
    );

    if (!updatedUser) {
      throw new Error('User not found');
    }

    return updatedUser;
  } catch (error) {
    throw error;
  }
};

/**
 * Delete a user from both Firebase and MongoDB
 *
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<void>}
 * @throws {Error} If user deletion fails
 */
const deleteUserAccount = async (firebaseUid) => {
  try {
    // Delete from Firebase
    await deleteUser(firebaseUid);

    // Delete from MongoDB
    const result = await User.deleteOne({ firebaseUid });

    if (result.deletedCount === 0) {
      throw new Error('User not found in database');
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Set a user as an admin
 *
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<void>}
 * @throws {Error} If setting admin role fails
 */
const setUserAsAdmin = async (firebaseUid) => {
  try {
    // Set custom claims in Firebase
    await setCustomUserClaims(firebaseUid, { admin: true });
  } catch (error) {
    throw error;
  }
};

/**
 * Send password reset email
 *
 * @param {string} email - User email
 * @returns {Promise<string>} Password reset link
 * @throws {Error} If sending reset email fails
 */
const sendPasswordResetEmail = async (email) => {
  try {
    return await generatePasswordResetLink(email);
  } catch (error) {
    throw error;
  }
};

/**
 * Send email verification
 *
 * @param {string} email - User email
 * @returns {Promise<string>} Email verification link
 * @throws {Error} If sending verification email fails
 */
const sendEmailVerification = async (email) => {
  try {
    return await generateEmailVerificationLink(email);
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createUser,
  getUserByPointId,
  getUserByFirebaseUid,
  getUserByEmail,
  updateUserProfile,
  deleteUserAccount,
  setUserAsAdmin,
  sendPasswordResetEmail,
  sendEmailVerification
};