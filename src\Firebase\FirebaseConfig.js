/**
 * Firebase Client Configuration
 *
 * This module provides the Firebase configuration for client-side applications.
 * It should be used by the web application and shared with the desktop application.
 *
 * IMPORTANT:
 * - This file contains configuration that is safe to expose to clients
 * - Do NOT include any secrets or service account keys here
 * - The API key is restricted by Firebase to your domain in production
 *
 * SETUP INSTRUCTIONS:
 * 1. Create a Firebase project at https://console.firebase.google.com/
 * 2. Register your web app in the Firebase console
 * 3. Copy the configuration values to your .env file
 * 4. For the desktop app, you'll need to include these values in your Java application
 *
 * @version 1.0.0
 */

// Firebase configuration object for client applications
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY || "your-api-key",
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || "your-project-id.firebaseapp.com",
  projectId: process.env.FIREBASE_PROJECT_ID || "your-project-id",
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || "your-project-id.appspot.com",
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || "your-messaging-sender-id",
  appId: process.env.FIREBASE_APP_ID || "your-app-id"
};

/**
 * Get Firebase configuration for client applications
 * 
 * @returns {Object} Firebase configuration object
 */
const getFirebaseConfig = () => {
  // In production, ensure all required config values are set
  if (process.env.NODE_ENV === 'production') {
    const requiredFields = [
      'FIREBASE_API_KEY',
      'FIREBASE_AUTH_DOMAIN',
      'FIREBASE_PROJECT_ID',
      'FIREBASE_STORAGE_BUCKET',
      'FIREBASE_MESSAGING_SENDER_ID',
      'FIREBASE_APP_ID'
    ];
    
    const missingFields = requiredFields.filter(field => !process.env[field]);
    
    if (missingFields.length > 0) {
      console.error(`Missing required Firebase configuration: ${missingFields.join(', ')}`);
      throw new Error('Incomplete Firebase configuration');
    }
  }
  
  return firebaseConfig;
};

/**
 * Get Firebase configuration as a JSON string
 * This is useful for passing to client applications
 * 
 * @returns {string} Firebase configuration as JSON string
 */
const getFirebaseConfigJson = () => {
  return JSON.stringify(getFirebaseConfig());
};

/**
 * Get Firebase configuration for Java desktop application
 * This formats the config in a way that can be used with the Firebase Java SDK
 * 
 * @returns {Object} Firebase configuration for Java
 */
const getFirebaseConfigForJava = () => {
  const config = getFirebaseConfig();
  
  // Format for Java Firebase SDK
  return {
    apiKey: config.apiKey,
    authDomain: config.authDomain,
    projectId: config.projectId,
    storageBucket: config.storageBucket,
    messagingSenderId: config.messagingSenderId,
    appId: config.appId
  };
};

module.exports = {
  getFirebaseConfig,
  getFirebaseConfigJson,
  getFirebaseConfigForJava
};
