/**
 * Community Dues Controller
 *
 * This module provides functions for managing community dues.
 * It handles dues creation, payment, and retrieval.
 */

const CommunityDues = require('../models/CommunityDues');
const Community = require('../models/Community');
const User = require('../models/User');
const Payment = require('../models/Payment');
const { generateUniqueId } = require('../../utils/idGenerator');

/**
 * Create new dues for a community member
 * 
 * @param {Object} duesData - Dues data
 * @param {string} duesData.communityId - Community ID
 * @param {string} duesData.pointId - Point ID of the member
 * @param {string} duesData.amount - Amount due
 * @param {string} duesData.dueDate - Due date (MM/DD/YYYY)
 * @returns {Promise<Object>} Created dues record
 */
const createDues = async (duesData) => {
  try {
    // Create the dues record
    const dues = await CommunityDues.create({
      CommunityID: duesData.communityId,
      PointID: duesData.pointId,
      Amount: duesData.amount,
      Status: "Pending",
      dueDate: duesData.dueDate,
      paidDate: ""
    });
    
    return dues;
  } catch (error) {
    throw error;
  }
};

/**
 * Create dues for all members of a community
 * 
 * @param {Object} duesData - Dues data
 * @param {string} duesData.communityId - Community ID
 * @param {string} duesData.amount - Amount due
 * @param {string} duesData.dueDate - Due date (MM/DD/YYYY)
 * @returns {Promise<Array>} Created dues records
 */
const createDuesForAllMembers = async (duesData) => {
  try {
    // Get the community
    const community = await Community.findOne({ CommunityId: duesData.communityId });
    if (!community) throw new Error('Community not found');
    
    // Get all members of the community
    const members = community.members.split(',').map(member => member.trim());
    
    // Create dues for each member
    const duesPromises = members.map(pointId => 
      createDues({
        communityId: duesData.communityId,
        pointId,
        amount: duesData.amount,
        dueDate: duesData.dueDate
      })
    );
    
    const createdDues = await Promise.all(duesPromises);
    
    // Update community total dues amount
    const totalAmount = parseFloat(community.totalDuesAmount.replace('$', '')) + 
                        (parseFloat(duesData.amount) * members.length);
    
    community.totalDuesAmount = `$${totalAmount.toFixed(2)}`;
    await community.save();
    
    return createdDues;
  } catch (error) {
    throw error;
  }
};

/**
 * Pay dues
 * 
 * @param {string} duesId - Dues ID
 * @param {string} paymentId - Payment ID
 * @returns {Promise<Object>} Updated dues record
 */
const payDues = async (duesId, paymentId) => {
  try {
    // Find the dues
    const dues = await CommunityDues.findById(duesId);
    if (!dues) throw new Error('Dues not found');
    
    // Find the payment
    const payment = await Payment.findOne({ PaymentId: paymentId });
    if (!payment) throw new Error('Payment not found');
    
    // Check if payment is completed
    if (payment.status !== "completed") {
      throw new Error('Payment is not completed');
    }
    
    // Update dues status
    dues.Status = "Paid";
    dues.paidDate = new Date().toLocaleDateString('en-US');
    await dues.save();
    
    return dues;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all dues for a community
 * 
 * @param {string} communityId - Community ID
 * @returns {Promise<Array>} List of dues
 */
const getCommunityDues = async (communityId) => {
  try {
    // Get all dues for this community
    const dues = await CommunityDues.find({ CommunityID: communityId });
    return dues;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all dues for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Array>} List of dues
 */
const getUserDues = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get all dues for this user
    const dues = await CommunityDues.find({ PointID: user.pointId });
    return dues;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all pending dues for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Array>} List of pending dues
 */
const getUserPendingDues = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get all pending dues for this user
    const dues = await CommunityDues.find({ 
      PointID: user.pointId,
      Status: "Pending"
    });
    
    return dues;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createDues,
  createDuesForAllMembers,
  payDues,
  getCommunityDues,
  getUserDues,
  getUserPendingDues
};
