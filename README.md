this is going to be the backend that connects to our mongoDB database that we will deploy using mongoAtlas. it will be connected to a desktop app (coded in java) that my users download from my webapp (coded in javascript). the desktop app will be where users make changes to the database that is then reflected in the webapp. and the webapp will also have functionality that updates the database such as creating a new account (this makes new records in the database that are used by the desktop app when referencing). we are planning to allow users on the desktop to upload content that we will store via telnyx that works in collaboration with our mongoDB system. 

(we have some code already for both the desktop app and webapp)

both the backend and webapp will be hosted with google cloud

we want to use springboot and websocket for the connections between our different systems (desktop app, backend, frontend/webapp) by utilizing API keys and httpe requests

the backend(this code) should handle all database schema, the desktop and web portions will send requests for database information to here, which will be processed and then sent back to the requestor

