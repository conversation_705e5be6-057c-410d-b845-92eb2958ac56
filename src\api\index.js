const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./routes/auth');
const communityRoutes = require('./routes/community');
const pointRoutes = require('./routes/point');
const publicRoutes = require('./routes/public');
const userRoutes = require('./routes/user');
const paymentRoutes = require('./routes/payment');
const dataRoutes = require('./routes/data');
const referralCodeRoutes = require('./routes/referralCode');
const communityDuesRoutes = require('./routes/communityDues');

// Map routes
router.use('/auth', authRoutes);
router.use('/communities', communityRoutes);
router.use('/points', pointRoutes);
router.use('/public', publicRoutes);
router.use('/user', userRoutes);
router.use('/payment', paymentRoutes);
router.use('/data', dataRoutes);
router.use('/referral-code', referralCodeRoutes);
router.use('/community-dues', communityDuesRoutes);

module.exports = router;