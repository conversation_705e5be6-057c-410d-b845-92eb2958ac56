const express = require('express');
const router = express.Router();
const communityController = require('../../MongoDB/controllers/communityController');
const { authenticate } = require('../../middleware/auth');

// All community routes require authentication
router.use(authenticate);

// Get user's communities
router.get('/', async (req, res) => {
  try {
    const communities = await communityController.getUserCommunities(req.user.uid);
    res.json(communities);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Create new community
router.post('/', async (req, res) => {
  try {
    const community = await communityController.createCommunity({
      name: req.body.name,
      ownerUid: req.user.uid,
      // Other community data
    });
    res.status(201).json(community);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;