const mongoose = require('mongoose');
const { Schema } = mongoose;

const communityDuesSchema = new Schema({
  _id: {
    type: Schema.Types.ObjectId,
    auto: true
  },
  Amount: {
    type: Number,
    required: true,
    default: 0.00,
    get: v => parseFloat(v).toFixed(2),
    set: v => parseFloat(v)
  },
  CommunityID: {
    type: String,
    required: true
  },
  PointID: {
    type: String,
    required: true
  },
  Status: {
    type: String,
    enum: ["Pending", "Paid"],
    default: "Pending"
  },
  dueDate: {
    type: Date,
    required: true
  },
  paidDate: {
    type: Date,
    default: null
  }
}, {
  toJSON: { getters: true },
  toObject: { getters: true },
  timestamps: true
});

// Virtual for formatted dates
communityDuesSchema.virtual('formattedDueDate').get(function() {
  return this.dueDate.toLocaleDateString('en-US');
});

communityDuesSchema.virtual('formattedPaidDate').get(function() {
  return this.paidDate ? this.paidDate.toLocaleDateString('en-US') : '';
});

// Index on common queries
communityDuesSchema.index({ CommunityID: 1 });
communityDuesSchema.index({ PointID: 1 });
communityDuesSchema.index({ Status: 1 });
communityDuesSchema.index({ dueDate: 1 });

module.exports = mongoose.model('CommunityDues', communityDuesSchema);
