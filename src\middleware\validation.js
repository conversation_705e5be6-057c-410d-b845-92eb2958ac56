const { body, param, validationResult } = require('express-validator');
const { ValidationError } = require('../utils/errors');

// Middleware to check for validation errors
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const validationError = new ValidationError('Validation failed');

    // Add each validation error to our custom error object
    errors.array().forEach(err => {
      validationError.addValidationError(err.path, err.msg);
    });

    // Pass the error to the error handler middleware
    return next(validationError);
  }
  next();
};

// User registration validation rules
const userValidationRules = [
  body('email')
    .isEmail().withMessage('Must provide a valid email')
    .normalizeEmail(),

  body('password')
    .isLength({ min: 8 }).withMessage('Password must be at least 8 characters')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number')
    .matches(/[^A-Za-z0-9]/).withMessage('Password must contain at least one special character'),

  body('username')
    .isLength({ min: 3, max: 30 }).withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers and underscores')
    .trim(),

  body('phoneNumber')
    .optional()
    .isMobilePhone().withMessage('Must provide a valid phone number')
];

// Community validation rules
const communityValidationRules = [
  body('name')
    .isLength({ min: 2, max: 50 }).withMessage('Community name must be between 2 and 50 characters')
    .trim(),

  body('settings')
    .isIn(['Public', 'Private']).withMessage('Settings must be either Public or Private'),

  body('CommunityShape')
    .optional()
    .isIn(['Tree', 'Circle', 'Grid']).withMessage('Community shape must be one of: Tree, Circle, Grid')
];

// Point validation rules
const pointValidationRules = [
  body('position.x')
    .optional()
    .isNumeric().withMessage('X position must be a number')
    .toFloat(),

  body('position.y')
    .optional()
    .isNumeric().withMessage('Y position must be a number')
    .toFloat(),

  body('position.z')
    .optional()
    .isNumeric().withMessage('Z position must be a number')
    .toFloat(),

  body('metadata')
    .optional()
    .isString().withMessage('Metadata must be a string'),

  body('previewFrame')
    .optional()
    .isString().withMessage('Preview frame must be a string'),

  param('communityId')
    .isString().withMessage('Community ID must be provided')
];

// Payment validation rules
const paymentValidationRules = [
  body('amount')
    .isNumeric().withMessage('Amount must be a number')
    .custom(value => parseFloat(value) > 0).withMessage('Amount must be greater than 0')
    .toFloat(),

  body('type')
    .isIn(['subscription', 'data-upgrade', 'dues']).withMessage('Type must be one of: subscription, data-upgrade, dues'),

  body('stripePaymentId')
    .optional()
    .isString().withMessage('Stripe payment ID must be a string')
];

// Community dues validation rules
const communityDuesValidationRules = [
  body('communityId')
    .isString().withMessage('Community ID is required'),

  body('pointId')
    .optional()
    .isString().withMessage('Point ID must be a string'),

  body('amount')
    .isNumeric().withMessage('Amount must be a number')
    .custom(value => parseFloat(value) > 0).withMessage('Amount must be greater than 0')
    .toFloat(),

  body('dueDate')
    .isISO8601().withMessage('Due date must be a valid date')
    .toDate()
];

// Data validation rules
const dataValidationRules = [
  body('usedBytes')
    .optional()
    .isNumeric().withMessage('Used bytes must be a number')
    .custom(value => parseInt(value) >= 0).withMessage('Used bytes must be a non-negative number')
    .toInt(),

  body('additionalGB')
    .optional()
    .isNumeric().withMessage('Additional GB must be a number')
    .custom(value => parseFloat(value) > 0).withMessage('Additional GB must be greater than 0')
    .toFloat()
];

// Referral code validation rules
const referralCodeValidationRules = [
  body('communityId')
    .isString().withMessage('Community ID is required'),

  body('code')
    .optional()
    .isString().withMessage('Code must be a string')
    .isLength({ min: 6, max: 20 }).withMessage('Code must be between 6 and 20 characters')
];

module.exports = {
  validate,
  userValidationRules,
  communityValidationRules,
  pointValidationRules,
  paymentValidationRules,
  communityDuesValidationRules,
  dataValidationRules,
  referralCodeValidationRules
};