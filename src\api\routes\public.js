const express = require('express');
const router = express.Router();
const { publicViewRateLimit } = require('../../middleware/rateLimiter');
const communityController = require('../../MongoDB/controllers/communityController');
const pointController = require('../../MongoDB/controllers/pointController');

// Apply rate limiting to all public routes
router.use(publicViewRateLimit);

// Get public community by ID
router.get('/community/:id', async (req, res) => {
  try {
    const community = await communityController.getPublicCommunityById(req.params.id);
    if (!community) return res.status(404).json({ error: 'Community not found' });
    
    res.json(community);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Get public points for a community
router.get('/points/:communityId', async (req, res) => {
  try {
    const points = await pointController.getPublicPointsByCommunityId(req.params.communityId);
    res.json(points);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;