/**
 * Data Controller
 *
 * This module provides functions for managing user data usage and limits.
 * It handles data record creation, updates, and retrieval.
 */

const Data = require('../models/Data');
const User = require('../models/User');
const Point = require('../models/Point');

/**
 * Create or update a data record for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Object>} Created or updated data record
 */
const createOrUpdateDataRecord = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Check if data record exists
    let dataRecord = await Data.findOne({ PointId: user.pointId });
    
    if (!dataRecord) {
      // Create new data record
      dataRecord = await Data.create({
        PointId: user.pointId,
        TotalData: "50 GB",
        DataAvailable: "50 GB",
        DataUsed: "0 GB",
        AmountPaid: "0.00",
        NextPurchase: "5.00"
      });
    }
    
    return dataRecord;
  } catch (error) {
    throw error;
  }
};

/**
 * Get data record for a user
 * 
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Object>} Data record
 */
const getUserDataRecord = async (firebaseUid) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get data record
    let dataRecord = await Data.findOne({ PointId: user.pointId });
    
    // Create record if it doesn't exist
    if (!dataRecord) {
      dataRecord = await createOrUpdateDataRecord(firebaseUid);
    }
    
    return dataRecord;
  } catch (error) {
    throw error;
  }
};

/**
 * Update data usage
 * 
 * @param {string} firebaseUid - Firebase UID
 * @param {number} usedBytes - Bytes used
 * @returns {Promise<Object>} Updated data record
 */
const updateDataUsage = async (firebaseUid, usedBytes) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get data record
    let dataRecord = await Data.findOne({ PointId: user.pointId });
    
    // Create record if it doesn't exist
    if (!dataRecord) {
      dataRecord = await createOrUpdateDataRecord(firebaseUid);
    }
    
    // Convert bytes to GB
    const usedGB = (usedBytes / (1024 * 1024 * 1024)).toFixed(2);
    
    // Parse current usage
    const currentUsedGB = parseFloat(dataRecord.DataUsed.split(' ')[0]) || 0;
    const totalGB = parseFloat(dataRecord.TotalData.split(' ')[0]) || 50;
    
    // Calculate new usage
    const newUsedGB = currentUsedGB + parseFloat(usedGB);
    const availableGB = Math.max(0, totalGB - newUsedGB);
    
    // Update data record
    dataRecord.DataUsed = `${newUsedGB.toFixed(2)} GB`;
    dataRecord.DataAvailable = `${availableGB.toFixed(2)} GB`;
    await dataRecord.save();
    
    // Update user and point records
    user.dataUsage = `${newUsedGB.toFixed(2)}`;
    await user.save();
    
    const point = await Point.findOne({ PointId: user.pointId });
    if (point) {
      point.dataUsage = `${newUsedGB.toFixed(2)}`;
      await point.save();
    }
    
    return dataRecord;
  } catch (error) {
    throw error;
  }
};

/**
 * Increase data limit
 * 
 * @param {string} firebaseUid - Firebase UID
 * @param {number} additionalGB - Additional GB to add
 * @param {string} paymentId - Payment ID
 * @returns {Promise<Object>} Updated data record
 */
const increaseDataLimit = async (firebaseUid, additionalGB, paymentId) => {
  try {
    // Get user's pointId from Firebase UID
    const user = await User.findOne({ firebaseUid });
    if (!user) throw new Error('User not found');
    
    // Get data record
    let dataRecord = await Data.findOne({ PointId: user.pointId });
    
    // Create record if it doesn't exist
    if (!dataRecord) {
      dataRecord = await createOrUpdateDataRecord(firebaseUid);
    }
    
    // Parse current limits
    const currentTotalGB = parseFloat(dataRecord.TotalData.split(' ')[0]) || 50;
    const currentUsedGB = parseFloat(dataRecord.DataUsed.split(' ')[0]) || 0;
    
    // Calculate new limits
    const newTotalGB = currentTotalGB + additionalGB;
    const availableGB = newTotalGB - currentUsedGB;
    
    // Update data record
    dataRecord.TotalData = `${newTotalGB.toFixed(2)} GB`;
    dataRecord.DataAvailable = `${availableGB.toFixed(2)} GB`;
    dataRecord.LastPaymentID = paymentId || dataRecord.LastPaymentID;
    await dataRecord.save();
    
    // Update user and point records
    user.dataLimit = `${newTotalGB.toFixed(2)}GB`;
    await user.save();
    
    const point = await Point.findOne({ PointId: user.pointId });
    if (point) {
      point.dataLimit = `${newTotalGB.toFixed(2)}GB`;
      await point.save();
    }
    
    return dataRecord;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createOrUpdateDataRecord,
  getUserDataRecord,
  updateDataUsage,
  increaseDataLimit
};
