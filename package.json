{"name": "homara-backend", "version": "1.0.0", "description": "Backend for Homara - a platform with website and desktop application components", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["homara", "backend", "api", "mongodb", "firebase", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "firebase-admin": "^11.8.0", "helmet": "^7.0.0", "hpp": "^0.2.3", "mongoose": "^7.1.1", "morgan": "^1.10.0", "xss-clean": "^0.1.1"}, "devDependencies": {"eslint": "^8.40.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "prettier": "^2.8.8", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}