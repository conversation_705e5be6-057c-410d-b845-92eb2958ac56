/**
 * Error handling middleware for the Homara backend
 *
 * This file contains middleware functions for handling errors and 404 routes.
 *
 * <AUTHOR> Name
 * @version 1.0.0
 */

const { NotFoundError } = require('../utils/errors');

/**
 * Not found middleware
 * Handles requests to routes that don't exist
 */
const notFound = (req, res, next) => {
  const error = new NotFoundError('Route', req.originalUrl);
  next(error);
};

/**
 * Error handler middleware
 * Handles all errors in the application
 *
 * In production, don't expose the stack trace to users
 */
const errorHandler = (err, req, res, next) => {
  // Log the error for server-side debugging
  console.error('Error:', err);

  // Default error response
  const errorResponse = {
    error: true,
    message: err.message || 'Something went wrong',
    statusCode: err.statusCode || 500
  };

  // Add validation errors if available
  if (err.name === 'ValidationError' && err.validationErrors && err.validationErrors.length > 0) {
    errorResponse.validationErrors = err.validationErrors;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
    if (err.name === 'DatabaseError' && err.originalError) {
      errorResponse.originalError = {
        message: err.originalError.message,
        stack: err.originalError.stack
      };
    }
  }

  // Set status code
  const statusCode = err.statusCode || (res.statusCode !== 200 ? res.statusCode : 500);
  res.status(statusCode);

  // Send error response
  res.json(errorResponse);
};

module.exports = { notFound, errorHandler };
